@forms @crud @e2e @comprehensive
Feature: Form Builder - Complete E2E CRUD Operations

  As a form author
  I want to perform complete CRUD operations on forms
  So that I can manage forms effectively through the UI

  Background:
    Given I am an authenticated user

  @crud @e2e @lifecycle @smoke
  Scenario: E2E-CRUD: Complete form lifecycle (Create → Edit → Read → Delete)
    # CREATE: Create new form via template
    Given I have navigated to the forms list page
    When I create a new form via "空白のフォーム" template
    Then the form should be created successfully
    And I should be in the form editor

    # UPDATE: Rename form to unique name and add content
    When I rename the form to a unique name
    And I add a "テキスト" field to the form
    And I add a "メールアドレス" field to the form
    And I save the form
    Then the save should complete successfully

    # READ: Navigate back and verify form exists
    When I navigate back to the forms list
    Then the form should appear in the forms list with the unique name
    And the form status should be "非公開"

    # UPDATE: Test complete status lifecycle (非公開 → 公開中 → 公開予約 → 公開終了 → 非公開)
    # Note: Test past dates first (allows status to remain clickable), then future dates (locks status)

    # First: Test 公開予約 with past time - should result in immediate publication (公開中)
    When I click on the form status chip
    And I select "公開予約" from the status menu
    And I handle the status change confirmation dialog with past dates
    Then the form status should be updated to "公開中"
    And I should see a status update success message

    # Second: Test 公開予約 with future time - should result in scheduling (公開予約)
    When I click on the form status chip
    And I select "公開予約" from the status menu
    And I handle the status change confirmation dialog with future dates
    Then the form status should be updated to "公開予約"
    And I should see a status update success message

    # Note: After 公開予約 with future dates, the status becomes locked and cannot be clicked
    # The form will remain in 公開予約 status, demonstrating the scheduling functionality
    # This completes the comprehensive status management test

    # DELETE: Remove form via UI dropdown menu
    When I open the more menu for the created form
    And I click the "フォームの削除" option from the menu
    And I confirm the form deletion in the dialog
    Then the form should be completely removed from the list
    And I should see a deletion success message
