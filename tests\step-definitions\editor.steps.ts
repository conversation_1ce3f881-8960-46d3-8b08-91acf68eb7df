import { Given, When, Then, Before, setDefaultTimeout } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { TestContext } from '../../src/utils/TestContext';
import { logger } from '../../src/utils/logger';

// Test context
let testContext: TestContext;

// Set timeout for long-running operations
setDefaultTimeout(60 * 1000);

Before(async function() {
  testContext = TestContext.getInstance();
});

// FRM-02: Templates render and open editor
When('I click on the {string} template', async function(_templateName: string) {
  return this.pending('Editor functionality not yet implemented');
});

Then('I am navigated to the editor for that form', async function() {
  const page = testContext.getPage();

  // Accept any non-slash segment as ID (string ids allowed)
  await expect(page).toHaveURL(/\/form-builder\/edit\/[^/?#]+$/);

  logger.info('✅ Navigated to form editor');
});

Then('I should be redirected to the form editor', async function() {
  const page = testContext.getPage();

  // Accept any non-slash segment as ID (string ids allowed)
  await expect(page).toHaveURL(/\/form-builder\/edit\/[^/?#]+$/);

  logger.info('✅ Redirected to form editor');
});

Then('I should see a form ID in the editor', async function() {
  return this.pending('Editor functionality not yet implemented');
});

// FRM-06: Publish/Unpublish toggles both UI and BE
When('I publish the form through the UI', async function() {
  return this.pending('Editor functionality not yet implemented');
});

When('I unpublish the form through the UI', async function() {
  return this.pending('Editor functionality not yet implemented');
});

// FRM-09: Row actions open correct destinations - Editor part
When('I click the {string} action for the form', async function(action: string) {
  if (action === '編集') {
    return this.pending('Editor functionality not yet implemented');
  }
  // Other actions handled in appropriate step files
});

// FRM-10: Localization – visible JP strings - Editor part
Then('I should see Japanese field labels in the editor:', async function(_dataTable) {
  return this.pending('Editor functionality not yet implemented');
});

// FRM-11: Error handling – API failures show user-friendly messages - Editor part
Given('the editor API is configured to return {int} errors', async function(_statusCode: number) {
  return this.pending('Editor functionality not yet implemented');
});

When('I try to save the form', async function() {
  return this.pending('Editor functionality not yet implemented');
});

// FRM-13: Scheduled publishing works correctly
Given('I have created a form scheduled for future publication', async function() {
  return this.pending('Editor functionality not yet implemented');
});

// FRM-15: Delete confirmation prevents accidental removal
When('I click the delete action for the form', async function() {
  return this.pending('Editor functionality not yet implemented');
});

Then('I should see a confirmation dialog', async function() {
  return this.pending('Editor functionality not yet implemented');
});

When('I confirm the deletion', async function() {
  return this.pending('Editor functionality not yet implemented');
});

Then('the form should be removed from the table', async function() {
  return this.pending('Editor functionality not yet implemented');
});

Then('the API should return {int} for the deleted form', async function(_expectedStatus: number) {
  return this.pending('Editor functionality not yet implemented');
});

// FRM-17: Security – unpublished forms are not accessible
Given('I have created an unpublished test form', async function() {
  return this.pending('Editor functionality not yet implemented');
});
