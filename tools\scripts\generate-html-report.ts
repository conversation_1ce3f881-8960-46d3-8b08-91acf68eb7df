#!/usr/bin/env ts-node

/**
 * HTML Test Report Generator Script
 * 
 * This script generates a comprehensive HTML test report from Playwright/Cucumber test results.
 * It can be run manually or integrated into CI/CD pipelines.
 * 
 * Usage:
 *   npm run report:html
 *   npx ts-node scripts/generate-html-report.ts
 *   node scripts/generate-html-report.js (if compiled)
 * 
 * Options:
 *   --config <path>    Path to custom config file
 *   --output <path>    Output directory for HTML report
 *   --open             Open the report in default browser after generation
 *   --no-cleanup       Skip cleanup of old reports
 *   --help             Show this help message
 */

import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { PremiumHtmlReportGenerator, ReportData } from '../../reports/generators/PremiumHtmlReportGenerator';
import { TestDataParser } from '../../src/utils/TestDataParser';
import { TestSummary } from '../../src/utils/ReportTypes';
import { logger } from '../../src/utils/logger';

const execAsync = promisify(exec);

interface ReportConfig {
  projectName: string;
  reportsDir: string;
  htmlOutputDir: string;
  maxReports: number;
  openAfterGeneration: boolean;
  cleanup: boolean;
  includeArtifacts: boolean;
  customCSS?: string;
  customJS?: string;
  logo?: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    slack?: {
      webhook: string;
      channel: string;
    };
    email?: {
      recipients: string[];
      smtpConfig: any;
    };
  };
}

class HtmlReportGeneratorScript {
  private config: ReportConfig;
  private parser: TestDataParser;
  private generator: PremiumHtmlReportGenerator;

  constructor(configPath?: string) {
    this.config = this.loadConfig(configPath);
    this.parser = new TestDataParser(this.config.reportsDir);
    this.generator = new PremiumHtmlReportGenerator(this.config.reportsDir, this.config.maxReports);
  }

  private loadConfig(configPath?: string): ReportConfig {
    const defaultConfig: ReportConfig = {
      projectName: 'SmoothContact Automation',
      reportsDir: 'reports',
      htmlOutputDir: 'reports/html',
      maxReports: 20,
      openAfterGeneration: false,
      cleanup: true,
      includeArtifacts: true,
      theme: 'auto',
      notifications: {}
    };

    if (configPath && fs.existsSync(configPath)) {
      try {
        const customConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        return { ...defaultConfig, ...customConfig };
      } catch (error) {
        logger.error(`Error loading config from ${configPath}:`, error);
        logger.info('Using default configuration');
      }
    }

    // Try to load from package.json
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        if (packageJson.htmlReportConfig) {
          return { ...defaultConfig, ...packageJson.htmlReportConfig };
        }
      } catch (error) {
        logger.error('Error loading config from package.json:', error);
      }
    }

    return defaultConfig;
  }

  public async generateReport(): Promise<string> {
    try {
      console.log('🚀 Starting HTML report generation...');
      logger.info('🚀 Starting HTML report generation...');

      // Parse test results
      console.log('📊 Parsing test results...');
      logger.info('📊 Parsing test results...');
      const originalData = await this.parser.parseTestResults();
      console.log('✅ Test data parsed, found', originalData.tests.length, 'tests');

      // Transform to premium format and enhance with config
      console.log('🔄 Transforming to premium format...');
      const reportData = this.transformToPremiumFormat(originalData);
      reportData.projectName = this.config.projectName;
      
      // Generate HTML report
      console.log('🎨 Generating HTML report...');
      logger.info('🎨 Generating HTML report...');
      const reportPath = await this.generator.generateReport(reportData);
      console.log('✅ HTML report generated at:', reportPath);
      
      // Save trend data for future reports
      await this.parser.saveTrendData({
        ...reportData.summary,
        flaky: reportData.summary.flaky || 0,
        retries: reportData.summary.retries || 0
      });
      
      // Log summary
      this.logReportSummary(reportData);
      
      // Send notifications if configured
      await this.sendNotifications(reportData, reportPath);
      
      // Open report if requested
      if (this.config.openAfterGeneration) {
        await this.openReport(reportPath);
      }
      
      logger.info(`✅ HTML report generated successfully: ${reportPath}`);
      return reportPath;
      
    } catch (error) {
      logger.error('❌ Error generating HTML report:', error);
      throw error;
    }
  }

  private transformToPremiumFormat(originalData: any): ReportData {
    const tests = originalData.tests || [];

    return {
      projectName: originalData.projectName || 'Test Report',
      environment: originalData.environment || 'development',
      runDate: originalData.runDate || new Date().toISOString(),
      runStart: originalData.runStart || new Date().toISOString(),
      runEnd: originalData.runEnd || new Date().toISOString(),
      totalDuration: originalData.totalDuration || 0,
      ci: originalData.ci,
      summary: this.calculateBasicSummary(tests),
      tests: tests.map((test: any) => ({
        id: test.id,
        name: test.name,
        status: test.status,
        feature: test.feature,
        browser: test.browser === 'cucumber' ? 'N/A' : test.browser,
        browserVersion: test.browserVersion,
        os: test.os,
        viewport: test.viewport,
        duration: test.duration,
        startTime: test.startTime,
        endTime: test.endTime,
        tags: test.tags || [],
        scenario: test.scenario || test.name,
        steps: test.steps?.map((step: any) => ({
          name: step.name || '(Unnamed)',
          status: step.status || 'passed',
          duration: step.duration || 0,
          error: typeof step.error === 'string' ? step.error : step.error?.message,
          screenshot: step.screenshot,
          startTime: step.startTime,
          endTime: step.endTime
        })) || [],
        error: test.error,
        stackTrace: test.stackTrace,
        artifacts: {
          screenshots: test.screenshots || [],
          videos: test.videos || [],
          traces: test.traces || [],
          logs: test.logs || [],
          attachments: test.attachments || []
        },
        retries: test.retries || 0,
        flakyHistory: test.flakyHistory,
        performance: test.performance
      })),
      history: originalData.history,
      metadata: {
        reportVersion: '2.0.0',
        generatedBy: 'PremiumHtmlReportGenerator',
        nodeVersion: process.version,
        playwrightVersion: 'Unknown',
        customConfig: {}
      }
    };
  }

  private calculateBasicSummary(tests: any[]): TestSummary {
    const total = tests.length;
    const passed = tests.filter(t => t.status === 'passed').length;
    const failed = tests.filter(t => t.status === 'failed').length;
    const skipped = tests.filter(t => t.status === 'skipped').length;
    const flaky = tests.filter(t => t.status === 'flaky').length;
    const retries = tests.reduce((sum, t) => sum + (t.retries || 0), 0);

    const passRate = total > 0 ? Math.round((passed / total) * 100) : 0;
    const avgDuration = total > 0 ? Math.round(tests.reduce((sum, t) => sum + (t.duration || 0), 0) / total) : 0;

    return {
      total,
      passed,
      failed,
      skipped,
      flaky,
      retries,
      passRate,
      avgDuration,
      byBrowser: {},
      byFeature: {},
      byTag: {}
    };
  }

  private logReportSummary(data: ReportData): void {
    const { summary } = data;
    const totalTests = summary.total;
    const passRate = totalTests > 0 ? ((summary.passed / totalTests) * 100).toFixed(1) : '0';
    
    logger.info('📈 Test Report Summary:');
    logger.info(`   Environment: ${data.environment}`);
    logger.info(`   Total Tests: ${totalTests}`);
    logger.info(`   ✅ Passed: ${summary.passed}`);
    logger.info(`   ❌ Failed: ${summary.failed}`);
    logger.info(`   ⏭️ Skipped: ${summary.skipped}`);
    logger.info(`   🔄 Flaky: ${summary.flaky}`);
    logger.info(`   🔁 Retries: ${summary.retries}`);
    logger.info(`   📊 Pass Rate: ${passRate}%`);
    logger.info(`   ⏱️ Total Duration: ${this.formatDuration(data.totalDuration || 0)}`);
  }

  private async sendNotifications(data: ReportData, reportPath: string): Promise<void> {
    try {
      // Slack notification
      if (this.config.notifications.slack) {
        await this.sendSlackNotification(data, reportPath);
      }

      // Email notification
      if (this.config.notifications.email) {
        await this.sendEmailNotification(data, reportPath);
      }
    } catch (error) {
      logger.error('Error sending notifications:', error);
    }
  }

  private async sendSlackNotification(data: ReportData, reportPath: string): Promise<void> {
    const { summary } = data;
    const passRate = summary.total > 0 ? ((summary.passed / summary.total) * 100).toFixed(1) : '0';
    const status = summary.failed > 0 ? '❌' : '✅';
    
    // Prepare Slack message (implementation would require actual webhook)
    logger.info(`📱 Slack notification prepared for ${data.projectName}`);
    logger.info(`   Status: ${status} | Pass Rate: ${passRate}% | Report: ${reportPath}`);
    logger.info(`   Results: ${summary.passed} passed, ${summary.failed} failed, ${summary.skipped} skipped, ${summary.flaky} flaky`);
  }

  private async sendEmailNotification(data: ReportData, reportPath: string): Promise<void> {
    // Email notification implementation would go here
    logger.info(`📧 Email notification prepared for ${data.projectName}`);
    logger.info(`   Report path: ${reportPath}`);
    logger.info('   SMTP implementation needed for actual email sending');
  }

  private async openReport(reportPath: string): Promise<void> {
    try {
      const absolutePath = path.resolve(reportPath);
      let command: string;

      switch (process.platform) {
        case 'darwin': // macOS
          command = `open "${absolutePath}"`;
          break;
        case 'win32': // Windows
          command = `start "" "${absolutePath}"`;
          break;
        default: // Linux and others
          command = `xdg-open "${absolutePath}"`;
          break;
      }

      await execAsync(command);
      logger.info(`🌐 Report opened in default browser: ${absolutePath}`);
    } catch (error) {
      logger.error('Error opening report in browser:', error);
      logger.info(`Please manually open: ${path.resolve(reportPath)}`);
    }
  }

  private formatDuration(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  }


}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  
  let configPath: string | undefined;
  let openAfterGeneration = false;

  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--config':
        configPath = args[++i];
        break;
      case '--open':
        openAfterGeneration = true;
        break;
      case '--help':
        console.log(`
HTML Test Report Generator

Usage:
  npm run report:html [options]
  npx ts-node scripts/generate-html-report.ts [options]

Options:
  --config <path>    Path to custom config file
  --open             Open the report in default browser after generation
  --help             Show this help message

Examples:
  npm run report:html --open
  npx ts-node scripts/generate-html-report.ts --config ./custom-config.json
        `);
        process.exit(0);
    }
  }

  try {
    console.log('🚀 Starting HTML report generation...');
    const generator = new HtmlReportGeneratorScript(configPath);
    if (openAfterGeneration) {
      generator['config'].openAfterGeneration = true;
    }
    console.log('📊 Generating report...');
    await generator.generateReport();
    console.log('✅ HTML report generation completed!');
  } catch (error) {
    console.error('❌ Script execution failed:', error);
    logger.error('Script execution failed:', error);
    process.exit(1);
  }
}

// Run directly
console.log('🚀 Starting HTML report generation script...');
main().then(() => {
  console.log('✅ Script completed successfully');
}).catch((error) => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});

export { HtmlReportGeneratorScript };
