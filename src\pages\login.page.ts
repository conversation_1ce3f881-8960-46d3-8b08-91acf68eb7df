import { Page, Locator } from '@playwright/test';
import { BasePage } from './BasePage';
import { logger } from '../utils/logger';

export class LoginPage extends BasePage {
  // Locators
  readonly emailInput: Locator;
  readonly passwordInput: Locator;
  readonly submitButton: Locator;
  readonly errorAlert: Locator;
  readonly logo: Locator;
  readonly pageTitle: Locator;
  readonly showPasswordToggle: Locator;
  readonly rememberMe: Locator;

  constructor(page: Page) {
    super(page, '');

    // Initialize locators based on actual Material-UI structure from HTML analysis
    this.emailInput = page.locator('input[name="email"]').first();
    this.passwordInput = page.locator('input[name="pwd"]').first();
    this.submitButton = page.locator('button[type="submit"]').first();
    this.errorAlert = page.locator('.MuiFormHelperText-root.Mui-error').first();
    this.logo = page.locator('img[alt="logo"]').first();
    this.pageTitle = page.locator('h1.MuiTypography-h5').first();
    this.showPasswordToggle = page.locator('.password-toggle, [data-testid="show-password"], .eye-icon');
    this.rememberMe = page.locator('input[type="checkbox"][name*="remember"], #remember-me');
  }

  /**
   * Navigate to login page
   */
  async goto(): Promise<void> {
    logger.step('Navigating to login page');

    // Get base URL from environment - use BASE_URL first, then fallback to STAGING_BASE_URL
    const baseUrl = process.env.BASE_URL || process.env.STAGING_BASE_URL || 'https://smoothcontact-web.bindec-app-stage.web-life.co.jp';

    // Navigate to the base URL (which should redirect to login if not authenticated)
    await this.page.goto(baseUrl);
    await this.waitForPageLoad();
    logger.info(`✅ Successfully navigated to login page: ${baseUrl}`);
  }

  /**
   * Check if login page is loaded with all required elements
   */
  async isLoaded(): Promise<boolean> {
    logger.step('Verifying login page is loaded');

    try {
      // Wait for page to be fully loaded
      await this.page.waitForLoadState('networkidle', { timeout: 15000 });

      // Wait for key elements to be visible with longer timeout
      await this.emailInput.waitFor({ state: 'visible', timeout: 15000 });
      await this.passwordInput.waitFor({ state: 'visible', timeout: 10000 });
      await this.submitButton.waitFor({ state: 'visible', timeout: 10000 });

      // Verify all required elements are visible
      await this.verifyElementVisible(this.emailInput);
      await this.verifyElementVisible(this.passwordInput);
      await this.verifyElementVisible(this.submitButton);

      // Check for logo and title (may not always be present)
      const logoVisible = await this.isElementVisible(this.logo);
      const titleVisible = await this.isElementVisible(this.pageTitle);

      logger.assertion(`✅ Login page loaded - Logo: ${logoVisible}, Title: ${titleVisible}`);
      return true;
    } catch (error) {
      logger.error(`❌ Login page not properly loaded: ${error}`);

      // Take screenshot for debugging
      await this.takeScreenshot('login-page-load-failed');

      return false;
    }
  }

  /**
   * Perform login with email and password
   */
  async login(email: string, password: string): Promise<void> {
    logger.step(`Attempting login with email: ${email}`);

    await this.goto();
    await this.page.getByRole('textbox', { name: /メール|email/i }).fill(email);
    await this.page.getByRole('textbox', { name: /パスワード|password/i }).fill(password);
    await this.page.getByRole('button', { name: /ログイン|Sign in/i }).click();
    await this.page.waitForURL(/form-builder|dashboard/i, { timeout: 15000 });

    logger.action('Login completed successfully');
  }

  /**
   * Get error message text with Unicode normalization
   */
  async getErrorText(): Promise<string> {
    logger.step('Getting error message text');

    try {
      // Look for any error message (email or password)
      const errorElements = this.page.locator('.MuiFormHelperText-root.Mui-error');
      await errorElements.first().waitFor({ state: 'visible', timeout: 10000 });

      // Get the first visible error message
      const errorText = await errorElements.first().textContent() || '';

      // Normalize Unicode to NFC to avoid glyph/width issues
      const normalizedText = errorText.normalize('NFC').trim();

      logger.info(`Error message: ${normalizedText}`);
      return normalizedText;
    } catch (error) {
      logger.warn(`No error message found or timeout: ${error}`);
      return '';
    }
  }

  /**
   * Get email field error message specifically
   */
  async getEmailErrorText(): Promise<string> {
    logger.step('Getting email error message text');

    try {
      // Look for error message associated with email field
      const emailErrorLocator = this.page.locator('input[name="email"]').locator('..').locator('.MuiFormHelperText-root.Mui-error');
      await emailErrorLocator.waitFor({ state: 'visible', timeout: 10000 });

      const errorText = await emailErrorLocator.textContent() || '';
      const normalizedText = errorText.normalize('NFC').trim();

      logger.info(`Email error message: ${normalizedText}`);
      return normalizedText;
    } catch (error) {
      logger.warn(`No email error message found: ${error}`);
      return '';
    }
  }

  /**
   * Get password field error message specifically
   */
  async getPasswordErrorText(): Promise<string> {
    logger.step('Getting password error message text');

    try {
      // Look for error message associated with password field
      const passwordErrorLocator = this.page.locator('input[name="pwd"]').locator('..').locator('.MuiFormHelperText-root.Mui-error');
      await passwordErrorLocator.waitFor({ state: 'visible', timeout: 10000 });

      const errorText = await passwordErrorLocator.textContent() || '';
      const normalizedText = errorText.normalize('NFC').trim();

      logger.info(`Password error message: ${normalizedText}`);
      return normalizedText;
    } catch (error) {
      logger.warn(`No password error message found: ${error}`);
      return '';
    }
  }

  /**
   * Toggle password visibility
   */
  async togglePasswordVisibility(): Promise<void> {
    logger.step('Toggling password visibility');
    
    const isToggleVisible = await this.isElementVisible(this.showPasswordToggle);
    if (isToggleVisible) {
      await this.clickElement(this.showPasswordToggle);
      logger.action('Password visibility toggled');
    } else {
      logger.warn('Password toggle not found on page');
    }
  }

  /**
   * Check or uncheck remember me checkbox
   */
  async rememberMeCheck(on: boolean): Promise<void> {
    logger.step(`Setting remember me to: ${on}`);
    
    const isRememberMeVisible = await this.isElementVisible(this.rememberMe);
    if (isRememberMeVisible) {
      const isChecked = await this.rememberMe.isChecked();
      
      if (on && !isChecked) {
        await this.clickElement(this.rememberMe);
        logger.action('Remember me checked');
      } else if (!on && isChecked) {
        await this.clickElement(this.rememberMe);
        logger.action('Remember me unchecked');
      }
    } else {
      logger.warn('Remember me checkbox not found on page');
    }
  }

  /**
   * Logout from the application
   */
  async logout(): Promise<void> {
    logger.step('Attempting logout');

    // First click on user avatar to open the dropdown menu
    const userAvatar = this.page.locator('.MuiAvatar-root').first();
    await userAvatar.waitFor({ state: 'visible', timeout: 5000 });
    await userAvatar.click();

    // Wait for menu to appear and click logout
    const logoutButton = this.page.locator('text=ログアウト').first();
    await logoutButton.waitFor({ state: 'visible', timeout: 5000 });
    await logoutButton.click();

    logger.action('Logout button clicked');
  }

  /**
   * Check if password is currently visible (not masked)
   */
  async isPasswordVisible(): Promise<boolean> {
    const passwordType = await this.passwordInput.getAttribute('type');
    const isVisible = passwordType === 'text';
    logger.info(`Password visibility: ${isVisible}`);
    return isVisible;
  }

  /**
   * Check if remember me is checked
   */
  async isRememberMeChecked(): Promise<boolean> {
    const isRememberMeVisible = await this.isElementVisible(this.rememberMe);
    if (isRememberMeVisible) {
      const isChecked = await this.rememberMe.isChecked();
      logger.info(`Remember me checked: ${isChecked}`);
      return isChecked;
    }
    return false;
  }

  /**
   * Wait for successful login redirect
   */
  async waitForLoginSuccess(): Promise<void> {
    logger.step('Waiting for successful login redirect');
    
    // Wait for redirect to dashboard or authenticated page (form-builder is the actual dashboard)
    await this.page.waitForURL(/form-builder|dashboard|home/, { timeout: 15000 });
    logger.assertion('✅ Successfully redirected after login');
  }

  /**
   * Check if error alert is visible
   */
  async isErrorVisible(): Promise<boolean> {
    const isVisible = await this.isElementVisible(this.errorAlert);
    logger.info(`Error alert visible: ${isVisible}`);
    return isVisible;
  }

  /**
   * Clear login form
   */
  async clearForm(): Promise<void> {
    logger.step('Clearing login form');
    
    await this.fillInput(this.emailInput, '', { clear: true });
    await this.fillInput(this.passwordInput, '', { clear: true });
    
    logger.action('Login form cleared');
  }

  /**
   * Submit form without filling fields (for validation testing)
   */
  async submitEmptyForm(): Promise<void> {
    logger.step('Submitting empty form');
    await this.clickElement(this.submitButton);
    logger.action('Empty form submitted');
  }

  /**
   * Enter email only
   */
  async enterEmail(email: string): Promise<void> {
    logger.step(`Entering email: ${email}`);
    await this.fillInput(this.emailInput, email);
  }

  /**
   * Enter password only
   */
  async enterPassword(password: string): Promise<void> {
    logger.step('Entering password');
    await this.fillInput(this.passwordInput, password);
  }

  /**
   * Submit the form
   */
  async submit(): Promise<void> {
    logger.step('Submitting login form');
    await this.clickElement(this.submitButton);
    logger.action('Login form submitted');
  }

  /**
   * Take screenshot for visual regression testing
   */
  async takeVisualSnapshot(name: string): Promise<void> {
    logger.step(`Taking visual snapshot: ${name}`);

    // Mask dynamic elements before screenshot
    await this.page.addStyleTag({
      content: `
        .timestamp, .session-id, .csrf-token, [data-dynamic="true"] {
          visibility: hidden !important;
        }
      `
    });

    // Use page.screenshot() instead of toHaveScreenshot() for Cucumber context
    const screenshotPath = `screenshots/${name}-${new Date().toISOString().replace(/[:.]/g, '-')}.png`;

    // Ensure screenshots directory exists
    const fs = require('fs');
    if (!fs.existsSync('screenshots')) {
      fs.mkdirSync('screenshots', { recursive: true });
    }

    await this.page.screenshot({
      path: screenshotPath,
      fullPage: true
    });

    logger.action(`Visual snapshot saved: ${screenshotPath}`);
  }
}
