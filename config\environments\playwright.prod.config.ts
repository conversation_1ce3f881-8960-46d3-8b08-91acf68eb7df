import { defineConfig } from '@playwright/test';
import baseConfig from '../playwright.config';

export default defineConfig({
  ...baseConfig,
  use: {
    ...baseConfig.use,
    baseURL: process.env.PROD_BASE_URL || 'https://prod.smoothcontact.com',
    headless: true,
  },
  workers: 1,
  retries: 2,
  reporter: [
    ['html', { outputFolder: 'reports/playwright-report-prod' }],
    ['json', { outputFile: 'reports/playwright-results-prod.json' }],
    ['allure-playwright', { outputFolder: 'reports/allure-results-prod' }],
  ],
});
