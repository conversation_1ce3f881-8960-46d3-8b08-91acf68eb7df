#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Validating SmoothContact Automation Framework Setup...\n');

const requiredFiles = [
  'package.json',
  'tsconfig.json',
  'playwright.config.ts',
  'cucumber.js',
  '.eslintrc.js',
  '.prettierrc',
  'README.md',
  '.gitignore',
  '.env.example'
];

const requiredDirectories = [
  'pages',
  'features',
  'step-definitions',
  'fixtures',
  'utils',
  'config',
  'src/types',
  'tests/e2e',
  '.github/workflows'
];

const requiredPackages = [
  '@playwright/test',
  '@cucumber/cucumber',
  'typescript',
  'winston',
  'dotenv',
  'allure-commandline'
];

let validationErrors = [];
let validationWarnings = [];

// Check required files
console.log('📁 Checking required files...');
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} - MISSING`);
    validationErrors.push(`Missing required file: ${file}`);
  }
});

// Check required directories
console.log('\n📂 Checking required directories...');
requiredDirectories.forEach(dir => {
  if (fs.existsSync(dir)) {
    console.log(`  ✅ ${dir}/`);
  } else {
    console.log(`  ❌ ${dir}/ - MISSING`);
    validationErrors.push(`Missing required directory: ${dir}`);
  }
});

// Check package.json and dependencies
console.log('\n📦 Checking package dependencies...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const allDependencies = {
    ...packageJson.dependencies,
    ...packageJson.devDependencies
  };

  requiredPackages.forEach(pkg => {
    if (allDependencies[pkg]) {
      console.log(`  ✅ ${pkg} (${allDependencies[pkg]})`);
    } else {
      console.log(`  ❌ ${pkg} - MISSING`);
      validationErrors.push(`Missing required package: ${pkg}`);
    }
  });
} catch (error) {
  console.log('  ❌ Error reading package.json');
  validationErrors.push('Cannot read package.json');
}

// Check TypeScript configuration
console.log('\n🔧 Checking TypeScript configuration...');
try {
  const tsConfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
  
  if (tsConfig.compilerOptions) {
    console.log('  ✅ TypeScript compiler options configured');
    
    if (tsConfig.compilerOptions.paths) {
      console.log('  ✅ Path mapping configured');
    } else {
      console.log('  ⚠️  Path mapping not configured');
      validationWarnings.push('Path mapping not configured in tsconfig.json');
    }
  } else {
    console.log('  ❌ TypeScript compiler options missing');
    validationErrors.push('TypeScript compiler options not configured');
  }
} catch (error) {
  console.log('  ❌ Error reading tsconfig.json');
  validationErrors.push('Cannot read tsconfig.json');
}

// Check Playwright configuration
console.log('\n🎭 Checking Playwright configuration...');
try {
  // Try to import the config (basic syntax check)
  const configPath = path.resolve('playwright.config.ts');
  if (fs.existsSync(configPath)) {
    console.log('  ✅ Playwright config file exists');
    
    const configContent = fs.readFileSync(configPath, 'utf8');
    if (configContent.includes('defineConfig')) {
      console.log('  ✅ Playwright config uses defineConfig');
    }
    if (configContent.includes('projects')) {
      console.log('  ✅ Multiple browser projects configured');
    }
    if (configContent.includes('reporter')) {
      console.log('  ✅ Reporters configured');
    }
  }
} catch (error) {
  console.log('  ❌ Error checking Playwright configuration');
  validationErrors.push('Playwright configuration error');
}

// Check Cucumber configuration
console.log('\n🥒 Checking Cucumber configuration...');
try {
  const cucumberConfig = require('../cucumber.js');
  if (cucumberConfig.default || cucumberConfig.dev || cucumberConfig.staging) {
    console.log('  ✅ Cucumber environments configured');
  } else {
    console.log('  ⚠️  Cucumber environments not fully configured');
    validationWarnings.push('Cucumber environments not fully configured');
  }
} catch (error) {
  console.log('  ❌ Error reading cucumber.js');
  validationErrors.push('Cannot read cucumber.js configuration');
}

// Check sample files
console.log('\n📝 Checking sample files...');
const sampleFiles = [
  'features/login.feature',
  'pages/LoginPage.ts',
  'pages/DashboardPage.ts',
  'step-definitions/login-steps.ts',
  'fixtures/testData.json'
];

sampleFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ⚠️  ${file} - Sample file missing`);
    validationWarnings.push(`Sample file missing: ${file}`);
  }
});

// Check node_modules
console.log('\n📚 Checking installation...');
if (fs.existsSync('node_modules')) {
  console.log('  ✅ node_modules directory exists');
  
  // Check if Playwright browsers are installed
  const playwrightPath = path.join('node_modules', '@playwright', 'test');
  if (fs.existsSync(playwrightPath)) {
    console.log('  ✅ Playwright installed');
  } else {
    console.log('  ❌ Playwright not installed');
    validationErrors.push('Playwright not installed');
  }
} else {
  console.log('  ❌ node_modules directory missing - run npm install');
  validationErrors.push('Dependencies not installed');
}

// Try to run basic commands
console.log('\n🧪 Testing basic commands...');
try {
  execSync('npx tsc --noEmit', { stdio: 'pipe' });
  console.log('  ✅ TypeScript compilation check passed');
} catch (error) {
  console.log('  ❌ TypeScript compilation check failed');
  validationErrors.push('TypeScript compilation errors');
}

try {
  execSync('npx eslint --version', { stdio: 'pipe' });
  console.log('  ✅ ESLint available');
} catch (error) {
  console.log('  ⚠️  ESLint not available');
  validationWarnings.push('ESLint not available');
}

try {
  execSync('npx prettier --version', { stdio: 'pipe' });
  console.log('  ✅ Prettier available');
} catch (error) {
  console.log('  ⚠️  Prettier not available');
  validationWarnings.push('Prettier not available');
}

// Summary
console.log('\n' + '='.repeat(60));
console.log('📊 VALIDATION SUMMARY');
console.log('='.repeat(60));

if (validationErrors.length === 0) {
  console.log('🎉 SUCCESS: Framework setup is complete and ready to use!');
} else {
  console.log(`❌ ERRORS FOUND: ${validationErrors.length}`);
  validationErrors.forEach((error, index) => {
    console.log(`  ${index + 1}. ${error}`);
  });
}

if (validationWarnings.length > 0) {
  console.log(`\n⚠️  WARNINGS: ${validationWarnings.length}`);
  validationWarnings.forEach((warning, index) => {
    console.log(`  ${index + 1}. ${warning}`);
  });
}

console.log('\n🚀 Next Steps:');
console.log('  1. Copy .env.example to .env and configure your settings');
console.log('  2. Run "npm run test" to execute sample tests');
console.log('  3. Run "npm run report" to generate test reports');
console.log('  4. Check README.md for detailed usage instructions');

process.exit(validationErrors.length > 0 ? 1 : 0);
