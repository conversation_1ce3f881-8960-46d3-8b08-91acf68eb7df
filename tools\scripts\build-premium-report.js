#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function generatePremiumReport() {
  const reportsDir = 'reports';
  const outputPath = path.join(reportsDir, 'premium-forms-report.html');

  // Read Cucumber JSON reports
  const cucumberReports = [];
  const jsonDir = path.join(reportsDir, 'json');
  
  if (fs.existsSync(jsonDir)) {
    const files = fs.readdirSync(jsonDir).filter(f => f.endsWith('.json'));
    files.forEach(file => {
      try {
        const content = fs.readFileSync(path.join(jsonDir, file), 'utf8');
        const data = JSON.parse(content);
        cucumberReports.push({ file, data });
      } catch (error) {
        console.warn(`Failed to parse ${file}:`, error.message);
      }
    });
  }

  // Generate HTML report
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmoothContact FRM Test Report</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 16px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header { 
            background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
            color: white; 
            padding: 40px; 
            text-align: center; 
        }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .header p { opacity: 0.9; font-size: 1.1rem; }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 20px; 
            padding: 40px; 
            background: #f8fafc;
        }
        .stat-card { 
            background: white; 
            padding: 24px; 
            border-radius: 12px; 
            text-align: center; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            border-left: 4px solid #3B82F6;
        }
        .stat-number { font-size: 2rem; font-weight: bold; color: #1E40AF; }
        .stat-label { color: #64748b; margin-top: 8px; }
        .scenarios { padding: 40px; }
        .scenario { 
            margin: 20px 0; 
            padding: 20px; 
            border-radius: 12px; 
            border-left: 4px solid #10B981;
        }
        .scenario.failed { border-left-color: #EF4444; background: #FEF2F2; }
        .scenario.passed { border-left-color: #10B981; background: #F0FDF4; }
        .scenario.skipped { border-left-color: #F59E0B; background: #FFFBEB; }
        .scenario-title { font-weight: bold; margin-bottom: 8px; }
        .scenario-status { 
            display: inline-block; 
            padding: 4px 12px; 
            border-radius: 20px; 
            font-size: 0.875rem; 
            font-weight: 500;
        }
        .status-passed { background: #DCFCE7; color: #166534; }
        .status-failed { background: #FEE2E2; color: #991B1B; }
        .status-skipped { background: #FEF3C7; color: #92400E; }
        .browser-section { margin: 30px 0; }
        .browser-header { 
            background: #1E293B; 
            color: white; 
            padding: 16px 24px; 
            border-radius: 8px 8px 0 0; 
            font-weight: bold;
        }
        .browser-content { 
            border: 1px solid #E2E8F0; 
            border-top: none; 
            border-radius: 0 0 8px 8px; 
            padding: 20px;
        }
        .footer { 
            background: #1E293B; 
            color: white; 
            padding: 30px; 
            text-align: center; 
        }
        .timestamp { opacity: 0.7; }
        .no-data { 
            text-align: center; 
            padding: 60px; 
            color: #64748b; 
        }
        @media (max-width: 768px) {
            .stats { grid-template-columns: 1fr; }
            .header h1 { font-size: 2rem; }
            body { padding: 10px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 SmoothContact FRM Test Report</h1>
            <p>Comprehensive Multi-Browser Test Results</p>
            <p class="timestamp">Generated: ${new Date().toLocaleString()}</p>
        </div>

        ${generateStatsSection(cucumberReports)}
        ${generateScenariosSection(cucumberReports)}

        <div class="footer">
            <p>🚀 Powered by Playwright + Cucumber + TypeScript</p>
            <p class="timestamp">Environment: ${process.env.BASE_URL || 'staging'}</p>
        </div>
    </div>
</body>
</html>`;

  // Ensure reports directory exists
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  fs.writeFileSync(outputPath, html);
  console.log(`✅ Premium report generated: ${outputPath}`);
  
  return outputPath;
}

function generateStatsSection(reports) {
  if (reports.length === 0) {
    return '<div class="no-data">No test data available</div>';
  }

  let totalScenarios = 0;
  let totalPassed = 0;
  let totalFailed = 0;
  let totalSkipped = 0;

  reports.forEach(report => {
    if (report.data && Array.isArray(report.data)) {
      report.data.forEach(feature => {
        if (feature.elements) {
          feature.elements.forEach(scenario => {
            totalScenarios++;
            if (scenario.steps && scenario.steps.length > 0) {
              const hasFailedStep = scenario.steps.some(step => step.result && step.result.status === 'failed');
              const hasSkippedStep = scenario.steps.some(step => step.result && step.result.status === 'skipped');
              
              if (hasFailedStep) {
                totalFailed++;
              } else if (hasSkippedStep) {
                totalSkipped++;
              } else {
                totalPassed++;
              }
            }
          });
        }
      });
    }
  });

  const passRate = totalScenarios > 0 ? ((totalPassed / totalScenarios) * 100).toFixed(1) : '0';

  return `
    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">${totalScenarios}</div>
            <div class="stat-label">Total Scenarios</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #10B981;">${totalPassed}</div>
            <div class="stat-label">Passed</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #EF4444;">${totalFailed}</div>
            <div class="stat-label">Failed</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #F59E0B;">${totalSkipped}</div>
            <div class="stat-label">Skipped</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #8B5CF6;">${passRate}%</div>
            <div class="stat-label">Pass Rate</div>
        </div>
    </div>`;
}

function generateScenariosSection(reports) {
  if (reports.length === 0) {
    return '<div class="scenarios"><div class="no-data">No scenarios to display</div></div>';
  }

  let scenariosHtml = '<div class="scenarios"><h2 style="margin-bottom: 30px;">📋 Test Scenarios</h2>';

  reports.forEach(report => {
    if (report.data && Array.isArray(report.data)) {
      const browserName = report.file.replace('.json', '').replace('cucumber-', '');
      
      scenariosHtml += `
        <div class="browser-section">
            <div class="browser-header">🌐 ${browserName.toUpperCase()}</div>
            <div class="browser-content">`;

      report.data.forEach(feature => {
        if (feature.elements) {
          feature.elements.forEach(scenario => {
            const hasFailedStep = scenario.steps && scenario.steps.some(step => step.result && step.result.status === 'failed');
            const hasSkippedStep = scenario.steps && scenario.steps.some(step => step.result && step.result.status === 'skipped');
            
            let status = 'passed';
            let statusClass = 'status-passed';
            if (hasFailedStep) {
              status = 'failed';
              statusClass = 'status-failed';
            } else if (hasSkippedStep) {
              status = 'skipped';
              statusClass = 'status-skipped';
            }

            scenariosHtml += `
              <div class="scenario ${status}">
                  <div class="scenario-title">${scenario.name}</div>
                  <span class="scenario-status ${statusClass}">${status.toUpperCase()}</span>
              </div>`;
          });
        }
      });

      scenariosHtml += '</div></div>';
    }
  });

  scenariosHtml += '</div>';
  return scenariosHtml;
}

if (require.main === module) {
  generatePremiumReport();
}

module.exports = { generatePremiumReport };
