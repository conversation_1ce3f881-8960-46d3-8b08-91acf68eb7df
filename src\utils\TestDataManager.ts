import * as fs from 'fs';
import * as path from 'path';
import { logger } from './logger';

export interface UserData {
  username: string;
  password: string;
  email: string;
  firstName: string;
  lastName: string;
  role?: string;
}

export interface TestDataSet {
  users: {
    admin: UserData;
    user: UserData;
    guest: UserData;
  };
  urls: {
    [key: string]: string;
  };
  testData: {
    [key: string]: any;
  };
}

export class TestDataManager {
  private static instance: TestDataManager;
  private dataCache: Map<string, any> = new Map();
  private readonly fixturesPath = path.join(process.cwd(), 'fixtures');

  private constructor() {
    this.ensureFixturesDirectory();
  }

  public static getInstance(): TestDataManager {
    if (!TestDataManager.instance) {
      TestDataManager.instance = new TestDataManager();
    }
    return TestDataManager.instance;
  }

  private ensureFixturesDirectory(): void {
    if (!fs.existsSync(this.fixturesPath)) {
      fs.mkdirSync(this.fixturesPath, { recursive: true });
      logger.info(`📁 Created fixtures directory: ${this.fixturesPath}`);
    }
  }

  /**
   * Load test data from JSON file
   */
  public loadTestData<T = any>(filename: string): T {
    const cacheKey = filename;
    
    if (this.dataCache.has(cacheKey)) {
      logger.debug(`📖 Loading cached data: ${filename}`);
      return this.dataCache.get(cacheKey);
    }

    const filePath = path.join(this.fixturesPath, `${filename}.json`);
    
    if (!fs.existsSync(filePath)) {
      logger.error(`❌ Test data file not found: ${filePath}`);
      throw new Error(`Test data file not found: ${filename}.json`);
    }

    try {
      const rawData = fs.readFileSync(filePath, 'utf8');
      const data = JSON.parse(rawData);
      
      this.dataCache.set(cacheKey, data);
      logger.info(`📊 Loaded test data: ${filename}`);
      
      return data;
    } catch (error) {
      logger.error(`❌ Failed to load test data from ${filename}: ${error}`);
      throw error;
    }
  }

  /**
   * Save test data to JSON file
   */
  public saveTestData(filename: string, data: any): void {
    const filePath = path.join(this.fixturesPath, `${filename}.json`);
    
    try {
      const jsonData = JSON.stringify(data, null, 2);
      fs.writeFileSync(filePath, jsonData, 'utf8');
      
      // Update cache
      this.dataCache.set(filename, data);
      
      logger.info(`💾 Saved test data: ${filename}`);
    } catch (error) {
      logger.error(`❌ Failed to save test data to ${filename}: ${error}`);
      throw error;
    }
  }

  /**
   * Get user data by role
   */
  public getUserData(role: 'admin' | 'user' | 'guest' = 'user'): UserData {
    const testData = this.loadTestData<TestDataSet>('testData');
    const userData = testData.users[role];
    
    if (!userData) {
      throw new Error(`User data not found for role: ${role}`);
    }
    
    logger.info(`👤 Retrieved user data for role: ${role}`);
    return userData;
  }

  /**
   * Get URL by environment and key
   */
  public getUrl(key: string, environment: string = process.env.NODE_ENV || 'development'): string {
    const envData = this.loadTestData(`urls.${environment}`);
    const url = envData[key];
    
    if (!url) {
      throw new Error(`URL not found for key: ${key} in environment: ${environment}`);
    }
    
    logger.info(`🔗 Retrieved URL: ${key} = ${url}`);
    return url;
  }

  /**
   * Generate random test data
   */
  public generateRandomData(): {
    email: string;
    username: string;
    firstName: string;
    lastName: string;
    phone: string;
    password: string;
    } {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    
    const data = {
      email: `test.user.${timestamp}.${random}@example.com`,
      username: `testuser${timestamp}${random}`,
      firstName: `FirstName${random}`,
      lastName: `LastName${random}`,
      phone: `+1555${String(random).padStart(7, '0')}`,
      password: `TestPass${random}!`,
    };
    
    logger.info(`🎲 Generated random test data for: ${data.username}`);
    return data;
  }

  /**
   * Get environment-specific configuration
   */
  public getEnvironmentConfig(environment: string = process.env.NODE_ENV || 'development'): any {
    try {
      return this.loadTestData(`config.${environment}`);
    } catch (error) {
      logger.warn(`⚠️ Environment config not found for: ${environment}, using default`);
      return this.loadTestData('config.default');
    }
  }

  /**
   * Clear cache
   */
  public clearCache(): void {
    this.dataCache.clear();
    logger.info('🧹 Test data cache cleared');
  }

  /**
   * Get all available test data files
   */
  public getAvailableDataFiles(): string[] {
    const files = fs.readdirSync(this.fixturesPath)
      .filter(file => file.endsWith('.json'))
      .map(file => file.replace('.json', ''));
    
    logger.info(`📋 Available test data files: ${files.join(', ')}`);
    return files;
  }

  /**
   * Validate test data structure
   */
  public validateTestData(filename: string, schema: any): boolean {
    try {
      const data = this.loadTestData(filename);
      // Simple validation - in a real scenario, you might use a library like Joi or Yup
      const isValid = this.simpleValidation(data, schema);
      
      if (isValid) {
        logger.info(`✅ Test data validation passed: ${filename}`);
      } else {
        logger.error(`❌ Test data validation failed: ${filename}`);
      }
      
      return isValid;
    } catch (error) {
      logger.error(`❌ Test data validation error: ${error}`);
      return false;
    }
  }

  private simpleValidation(data: any, schema: any): boolean {
    // Simple validation logic - extend as needed
    for (const key in schema) {
      if (!(key in data)) {
        return false;
      }
    }
    return true;
  }
}
