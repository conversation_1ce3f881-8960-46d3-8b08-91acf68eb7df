@forms @list
Feature: Forms List Page - Advanced UI Component Testing

  Background:
    Given I am logged in
    And I navigate to the forms list page

  @FRM-21 @ui-components @filter-panel
  Scenario: FRM-21: Advanced Filter Panel Functionality
    # Test the comprehensive filter panel with status dropdown and date range
    Given I am on the forms list page

    # Test 1: Filter panel expand/collapse
    When I click the filter icon to open the filter panel
    Then I should see the filter panel with title "フィルター"
    And I should see the close button in the filter panel
    And I should see the status dropdown labeled "フォームのステータス"
    And I should see date range inputs for "公開開始日時" and "公開終了日時"
    And I should see "クリア" and "フィルターを適用" buttons

    When I click the close button in the filter panel
    Then the filter panel should be hidden

    # Test 2: Status dropdown filtering
    When I open the filter panel
    And I click on the status dropdown "フォームのステータス"
    Then I should see status options in the dropdown
    When I select a status from the dropdown
    And I click "フィルターを適用" in the filter panel
    Then the forms list should be filtered by the selected status
    And the filter panel should close

    # Test 3: Date range filtering
    When I open the filter panel
    And I set the "公開開始日時" to a specific date
    And I set the "公開終了日時" to a specific date
    And I click "フィルターを適用" in the filter panel
    Then the forms list should be filtered by the date range

    # Test 4: Clear filters
    When I open the filter panel
    And I click the "クリア" button
    Then all filter inputs should be cleared
    And the "フィルターを適用" button should be disabled in the filter panel

    # Test 5: Filter apply button states
    When the filter panel has no changes
    Then the "フィルターを適用" button should be disabled in the filter panel
    When I make changes to any filter
    Then the "フィルターを適用" button should be enabled in the filter panel

  @FRM-22 @ui-components @form-creation-panel
  Scenario: FRM-22: Form Creation Panel Expand/Collapse
    # Test the form creation template selection panel
    Given I am on the forms list page

    # Test 1: Initial state
    Then I should see the form creation section with "新しいフォームを作成"
    And I should see the expand/collapse arrow button

    # Test 2: Panel expansion
    When I click the expand arrow in the form creation section
    Then the template selection panel should expand
    And I should see all template cards
    And the arrow should change to collapse state

    # Test 3: Panel collapse
    When I click the collapse arrow in the form creation section
    Then the template selection panel should collapse
    And the template cards should be hidden
    And the arrow should change to expand state

    # Test 4: Template selection from expanded panel
    When the form creation panel is expanded
    And I click on a template card
    Then I should be navigated to the form editor
    And the form should be created with the selected template

  @FRM-23 @ui-components @advanced-pagination
  Scenario: FRM-23: Advanced Pagination Component Testing
    # Test comprehensive pagination functionality and edge cases
    Given I am on the forms list page with multiple pages of data

    # Test 1: Rows per page dropdown interaction
    When I click on the rows per page dropdown
    Then I should see options for different page sizes
    When I select "10" from the rows per page dropdown
    Then the table should display up to 10 rows
    And the pagination info should update to reflect the new page size
    And the URL should include "perPage=10"

    # Test 2: Pagination button states on first page
    When I am on the first page
    Then the "first page" button should be disabled
    And the "previous page" button should be disabled
    And the "next page" button should be enabled (if more pages exist)
    And the "last page" button should be enabled (if more pages exist)

    # Test 3: Pagination button states on last page
    When I navigate to the last page
    Then the "next page" button should be disabled
    And the "last page" button should be disabled
    And the "previous page" button should be enabled
    And the "first page" button should be enabled

    # Test 4: Pagination info accuracy
    When I am on page 2 with 5 rows per page
    Then the pagination info should show "6–10 of X" format
    And the displayed row count should match the pagination info

    # Test 5: Direct page navigation via URL
    When I navigate directly to "?page=3&perPage=10"
    Then I should be on page 3 with 10 rows per page
    And the pagination controls should reflect the current state
    And the table should show the correct data range

  @FRM-24 @ui-components @responsive-behavior
  Scenario: FRM-24: UI Component Responsive Behavior
    # Test how UI components behave across different screen sizes
    Given I am on the forms list page

    # Test 1: Filter panel on mobile
    When I resize the browser to mobile width
    Then the filter panel should adapt to mobile layout
    And all filter controls should remain accessible

    # Test 2: Pagination on mobile
    When the screen is mobile-sized
    Then the pagination controls should be touch-friendly
    And the rows per page dropdown should be easily accessible

    # Test 3: Form creation panel on tablet
    When I resize the browser to tablet width
    Then the form creation panel should maintain usability
    And template cards should arrange appropriately

    # Test 4: Table responsiveness
    When the screen width is reduced
    Then the table should handle column overflow gracefully
    And horizontal scrolling should be available if needed
    And important columns should remain visible