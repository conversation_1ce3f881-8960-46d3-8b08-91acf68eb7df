name: E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to run tests against'
        required: true
        default: 'staging'
        type: choice
        options:
          - development
          - staging
          - production
      tags:
        description: 'Cucumber tags to run (e.g., @smoke, @regression)'
        required: false
        default: '@smoke'
      browser:
        description: 'Browser to run tests on'
        required: true
        default: 'chromium'
        type: choice
        options:
          - chromium
          - firefox
          - webkit
          - all

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    
    strategy:
      fail-fast: false
      matrix:
        browser: ${{ github.event.inputs.browser == 'all' && fromJSON('["chromium", "firefox", "webkit"]') || fromJSON(format('["{0}"]', github.event.inputs.browser || 'chromium')) }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps ${{ matrix.browser }}

      - name: Create logs directory
        run: mkdir -p logs reports

      - name: Set environment variables
        run: |
          echo "NODE_ENV=${{ github.event.inputs.environment || 'staging' }}" >> $GITHUB_ENV
          echo "HEADLESS=true" >> $GITHUB_ENV
          echo "CI=true" >> $GITHUB_ENV
          echo "BROWSER=${{ matrix.browser }}" >> $GITHUB_ENV

      - name: Run E2E tests
        run: |
          if [ "${{ github.event.inputs.tags }}" != "" ]; then
            npm run test:tag -- "${{ github.event.inputs.tags }}"
          else
            npm run test:headless
          fi
        env:
          NODE_ENV: ${{ github.event.inputs.environment || 'staging' }}
          HEADLESS: true
          CI: true

      - name: Generate Allure report
        if: always()
        run: |
          npm run report || echo "Report generation failed"

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-${{ matrix.browser }}-${{ github.run_number }}
          path: |
            reports/
            logs/
            test-results/
          retention-days: 30

      - name: Upload Playwright report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report-${{ matrix.browser }}-${{ github.run_number }}
          path: reports/playwright-report/
          retention-days: 30

      - name: Upload Allure results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: allure-results-${{ matrix.browser }}-${{ github.run_number }}
          path: reports/allure-results/
          retention-days: 30

      - name: Comment PR with test results
        if: github.event_name == 'pull_request' && always()
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const path = require('path');
            
            let comment = `## 🎭 E2E Test Results - ${{ matrix.browser }}\n\n`;
            
            // Try to read cucumber report
            try {
              const reportPath = 'reports/cucumber-report.json';
              if (fs.existsSync(reportPath)) {
                const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
                const totalScenarios = report.length;
                const passedScenarios = report.filter(r => r.status === 'passed').length;
                const failedScenarios = totalScenarios - passedScenarios;
                
                comment += `### 📊 Summary\n`;
                comment += `- **Total Scenarios**: ${totalScenarios}\n`;
                comment += `- **Passed**: ${passedScenarios} ✅\n`;
                comment += `- **Failed**: ${failedScenarios} ❌\n\n`;
                
                if (failedScenarios > 0) {
                  comment += `### ❌ Failed Scenarios\n`;
                  report.filter(r => r.status === 'failed').forEach(scenario => {
                    comment += `- ${scenario.name}\n`;
                  });
                }
              }
            } catch (error) {
              comment += `Could not parse test results: ${error.message}\n`;
            }
            
            comment += `\n### 📎 Artifacts\n`;
            comment += `- [Test Results](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})\n`;
            comment += `- [Screenshots & Videos](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})\n`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

  deploy-reports:
    needs: e2e-tests
    runs-on: ubuntu-latest
    if: always() && github.ref == 'refs/heads/main'
    
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts

      - name: Setup Pages
        uses: actions/configure-pages@v4

      - name: Build combined report
        run: |
          mkdir -p public
          echo "<h1>SmoothContact E2E Test Reports</h1>" > public/index.html
          echo "<ul>" >> public/index.html
          for dir in artifacts/*/; do
            if [ -d "$dir" ]; then
              dirname=$(basename "$dir")
              echo "<li><a href=\"$dirname/\">$dirname</a></li>" >> public/index.html
              cp -r "$dir" "public/"
            fi
          done
          echo "</ul>" >> public/index.html

      - name: Upload to Pages
        uses: actions/upload-pages-artifact@v3
        with:
          path: public

  deploy-pages:
    needs: deploy-reports
    runs-on: ubuntu-latest
    if: always() && github.ref == 'refs/heads/main'
    
    permissions:
      pages: write
      id-token: write
    
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
