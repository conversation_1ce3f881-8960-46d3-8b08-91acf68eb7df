# Comprehensive Preview Testing Plan

## Overview

This document outlines the comprehensive testing strategy for Form Builder Preview functionality, covering single elements, multiple elements, and all device views (Desktop, Tablet, Mobile) with complete flow validation.

## 🎯 Testing Objectives

The preview testing ensures that:
1. **Single Element Preview** - Individual form elements display correctly across all views
2. **Multiple Element Preview** - Complex forms with multiple elements render properly
3. **Device Responsiveness** - All views (Desktop/Tablet/Mobile) adapt correctly  
4. **Configuration Persistence** - Element settings from editor are preserved in preview
5. **Navigation Flow** - Seamless transition between editor and preview with state persistence

## 📋 Test Scenarios Implemented

### Single Element Testing (FB-91)
- **Scope**: All 12 form element types
- **Flow**: Add element → Save → Preview → Test all device views
- **Coverage**: Desktop, Tablet, Mobile responsive validation
- **Elements**: テキスト, 段落テキスト, メールアドレス, 電話番号, 日時, 氏名, 住所, 生年月日, ラジオボタン, チェックボックス, プルダウン, 添付ファイル

### Multiple Element Testing (FB-92)
- **Scope**: 4 diverse elements in one form
- **Flow**: Add multiple elements → Save → Preview → Test all views
- **Validation**: Element order preservation, responsive stacking
- **Elements**: テキスト, メールアドレス, ラジオボタン, チェックボックス

### Configuration Validation (FB-93)
- **Scope**: Complex element configurations
- **Features**: Custom labels, required fields, option configurations
- **Validation**: Settings preserved across all device views
- **Interactions**: Radio button and checkbox functionality testing

### Required Field Validation (FB-94)
- **Scope**: Form validation in preview
- **Features**: Required indicators, validation errors, successful submission
- **Cross-view**: Consistent validation behavior across devices

### Layout Stress Testing (FB-95)
- **Scope**: 10-element complex form
- **Features**: Ordering, visibility, responsiveness, scrolling
- **Performance**: No cut-off or overlapping elements

### Navigation Flow Testing (FB-96, FB-97, FB-98)
- **Scope**: Complete editor-preview-editor cycle
- **Features**: State persistence, configuration retention
- **Edge Cases**: Unsaved changes handling, auto-save validation

## 🛠️ Technical Implementation

### Device View Controls
The preview page includes three device view buttons:
```html
<button>ComputerIcon</button>   <!-- Desktop -->
<button>TabletMacIcon</button>   <!-- Tablet -->
<button>SmartphoneIcon</button>  <!-- Mobile -->
```

### Step Definitions Added
- **Responsive Validation**: Element display validation for each device type
- **Configuration Testing**: Label, required field, and option verification
- **Navigation Flow**: Editor-preview transitions with state persistence
- **Interaction Testing**: Form element functionality validation
- **Layout Validation**: Responsive behavior and element positioning

### Element Type Mapping
```typescript
const elementSelectors: Record<string, string> = {
  'テキスト': 'input[type="text"], input:not([type])',
  'メールアドレス': 'input[type="email"]',
  'ラジオボタン': 'input[type="radio"]',
  'チェックボックス': 'input[type="checkbox"]',
  'プルダウン': 'select',
  // ... additional mappings
};
```

## 🔍 Key Validation Points

### 1. Element Display Validation
- ✅ Element visibility across all device views
- ✅ Responsive layout adaptation
- ✅ Configuration preservation (labels, required indicators)
- ✅ Interactive functionality (radio/checkbox selections)

### 2. Layout Responsiveness
- ✅ Desktop: Optimal horizontal space usage
- ✅ Tablet: Medium-width responsive layout
- ✅ Mobile: Vertical stacking for touch interaction

### 3. Navigation & State Persistence
- ✅ Preview → Editor navigation maintains form state
- ✅ Element configurations preserved across transitions
- ✅ Unsaved changes handling with auto-save indicators

### 4. Form Functionality
- ✅ Required field validation in preview
- ✅ Form submission testing
- ✅ Cross-device validation consistency

## 🚀 Running the Tests

### Individual Test Execution
```bash
# Single element comprehensive testing
npx cucumber-js --tags "@preview and @single-element and @comprehensive"

# Multiple element testing
npx cucumber-js --tags "@preview and @multiple-elements and @comprehensive"

# Configuration validation
npx cucumber-js --tags "@preview and @configuration-validation"

# Navigation flow testing  
npx cucumber-js --tags "@preview and @navigation"
```

### Complete Preview Test Suite
```bash
# All preview scenarios
npx cucumber-js --tags "@preview"
```

## 📊 Test Coverage Matrix

| Element Type | Single | Multiple | Config | Required | Mobile | Tablet | Desktop |
|--------------|--------|----------|--------|----------|---------|---------|---------|
| テキスト     | ✅     | ✅       | ✅     | ✅       | ✅      | ✅      | ✅      |
| メールアドレス | ✅     | ✅       | ✅     | ✅       | ✅      | ✅      | ✅      |
| ラジオボタン  | ✅     | ✅       | ✅     | ✅       | ✅      | ✅      | ✅      |
| チェックボックス | ✅   | ✅       | ✅     | ✅       | ✅      | ✅      | ✅      |
| プルダウン    | ✅     | ✅       | ✅     | ✅       | ✅      | ✅      | ✅      |
| 段落テキスト  | ✅     | ✅       | ✅     | ✅       | ✅      | ✅      | ✅      |
| その他8要素   | ✅     | ✅       | ✅     | ✅       | ✅      | ✅      | ✅      |

## 🔄 Flow Diagram

```mermaid
graph TD
    A[Form Builder Editor] --> B[Add Elements]
    B --> C[Configure Elements] 
    C --> D[Save Form]
    D --> E[Click Preview]
    E --> F[Preview Page Loads]
    
    F --> G[Desktop View]
    F --> H[Tablet View]
    F --> I[Mobile View]
    
    G --> G1[Verify Element Display]
    H --> H1[Verify Responsive Layout]
    I --> I1[Verify Mobile Stacking]
    
    G1 --> J[Test Interactions]
    H1 --> J
    I1 --> J
    
    J --> K[Navigate Back to Editor]
    K --> L[Verify State Persistence]
```

## 📝 Test Scenarios Summary

### Basic Preview Flow
1. **FB-91**: Comprehensive single element preview across all views (12 elements × 3 views = 36 test combinations)
2. **FB-92**: Multiple elements preview with comprehensive view testing
3. **FB-93**: Complex element configuration validation
4. **FB-94**: Required fields and validation testing
5. **FB-95**: Layout stress test with 10 elements

### Advanced Flow Testing  
6. **FB-96**: Complete preview flow with editor navigation
7. **FB-97**: Preview back navigation preserves editor state
8. **FB-98**: Unsaved changes handling in preview

## ✅ Quality Assurance

### Pre-requisites Validated
- ✅ Form Builder Editor page accessibility
- ✅ Element palette functionality
- ✅ Canvas drag-and-drop operations
- ✅ Save functionality
- ✅ Preview button availability

### Cross-Device Compatibility
- ✅ Desktop viewport optimization
- ✅ Tablet responsive behavior
- ✅ Mobile touch-friendly layout
- ✅ Consistent functionality across all views

### Error Handling
- ✅ Empty form preview handling
- ✅ Unsaved changes prompts
- ✅ Navigation error recovery
- ✅ Element configuration failures

## 🎯 Success Criteria

The preview functionality is considered fully validated when:

1. **All 12 element types** display correctly in all 3 device views
2. **Multiple element forms** maintain proper ordering and responsiveness
3. **Configuration settings** (labels, required fields, options) are preserved
4. **Navigation flow** works seamlessly with state persistence
5. **Form validation** behaves consistently across device views
6. **Complex layouts** handle stress testing without issues
7. **Error scenarios** are handled gracefully

---

*This comprehensive testing plan ensures the Form Builder Preview functionality meets all quality requirements and provides an excellent user experience across all device types and usage scenarios.*
