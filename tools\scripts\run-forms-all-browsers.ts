#!/usr/bin/env ts-node

import { spawn } from 'child_process';
import { logger } from '../utils/logger';
import * as fs from 'fs';
import * as path from 'path';

interface TestResult {
  browser: string;
  success: boolean;
  duration: number;
  scenarios: number;
  passed: number;
  failed: number;
  skipped: number;
  error?: string;
}

const browsers = ['chromium', 'firefox', 'webkit'];
const results: TestResult[] = [];

async function runTestsForBrowser(browser: string): Promise<TestResult> {
  return new Promise((resolve) => {
    const startTime = Date.now();
    logger.info(`🚀 Starting FRM tests for ${browser}...`);

    const env = {
      ...process.env,
      BROWSER: browser,
      TAGS: '@forms',
      HEADLESS: process.env.HEADLESS || 'true'
    };

    const child = spawn('npm', ['run', 'test:forms'], {
      env,
      stdio: 'pipe',
      shell: true
    });

    let output = '';
    let errorOutput = '';

    child.stdout?.on('data', (data) => {
      const text = data.toString();
      output += text;
      console.log(`[${browser}] ${text}`);
    });

    child.stderr?.on('data', (data) => {
      const text = data.toString();
      errorOutput += text;
      console.error(`[${browser}] ${text}`);
    });

    child.on('close', (code) => {
      const duration = Date.now() - startTime;
      const success = code === 0;

      // Parse results from output
      const scenarioMatch = output.match(/(\d+) scenario/);
      const passedMatch = output.match(/(\d+) passed/);
      const failedMatch = output.match(/(\d+) failed/);
      const skippedMatch = output.match(/(\d+) skipped/);

      const result: TestResult = {
        browser,
        success,
        duration,
        scenarios: scenarioMatch ? parseInt(scenarioMatch[1]) : 0,
        passed: passedMatch ? parseInt(passedMatch[1]) : 0,
        failed: failedMatch ? parseInt(failedMatch[1]) : 0,
        skipped: skippedMatch ? parseInt(skippedMatch[1]) : 0,
        error: success ? '' : errorOutput || 'Unknown error'
      };

      if (success) {
        logger.info(`✅ ${browser} tests completed successfully in ${duration}ms`);
      } else {
        logger.error(`❌ ${browser} tests failed in ${duration}ms`);
      }

      resolve(result);
    });
  });
}

async function generateReport(results: TestResult[]): Promise<void> {
  const reportDir = 'reports/multi-browser';
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }

  const reportPath = path.join(reportDir, 'forms-multi-browser-report.html');
  
  const html = `
<!DOCTYPE html>
<html>
<head>
    <title>FRM Multi-Browser Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .browser-result { margin: 20px 0; padding: 15px; border-radius: 8px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .failure { background: #f8d7da; border: 1px solid #f5c6cb; }
        .stats { display: flex; gap: 20px; margin: 10px 0; }
        .stat { padding: 5px 10px; background: #e9ecef; border-radius: 4px; }
        .summary { background: #e7f3ff; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; }
        .error { color: #dc3545; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 FRM Multi-Browser Test Report</h1>
        <p>Generated: ${new Date().toISOString()}</p>
        <p>Environment: ${process.env.BASE_URL || 'staging'}</p>
    </div>

    <div class="summary">
        <h2>📊 Summary</h2>
        <table>
            <tr>
                <th>Browser</th>
                <th>Status</th>
                <th>Scenarios</th>
                <th>Passed</th>
                <th>Failed</th>
                <th>Skipped</th>
                <th>Duration</th>
            </tr>
            ${results.map(result => `
            <tr>
                <td>${result.browser}</td>
                <td>${result.success ? '✅ PASS' : '❌ FAIL'}</td>
                <td>${result.scenarios}</td>
                <td>${result.passed}</td>
                <td>${result.failed}</td>
                <td>${result.skipped}</td>
                <td>${(result.duration / 1000).toFixed(1)}s</td>
            </tr>
            `).join('')}
        </table>
    </div>

    ${results.map(result => `
    <div class="browser-result ${result.success ? 'success' : 'failure'}">
        <h3>${result.browser.toUpperCase()} ${result.success ? '✅' : '❌'}</h3>
        <div class="stats">
            <div class="stat">Scenarios: ${result.scenarios}</div>
            <div class="stat">Passed: ${result.passed}</div>
            <div class="stat">Failed: ${result.failed}</div>
            <div class="stat">Duration: ${(result.duration / 1000).toFixed(1)}s</div>
        </div>
        ${result.error ? `<div class="error">Error: ${result.error}</div>` : ''}
    </div>
    `).join('')}

    <div class="header">
        <h2>🎯 Next Steps</h2>
        <ul>
            <li>Check individual browser reports in <code>reports/</code> directory</li>
            <li>Review screenshots and videos for failed tests</li>
            <li>Run <code>npm run report:html</code> for detailed Playwright report</li>
        </ul>
    </div>
</body>
</html>
  `;

  fs.writeFileSync(reportPath, html);
  logger.info(`📊 Multi-browser report generated: ${reportPath}`);
}

async function main() {
  logger.info('🚀 Starting FRM tests across all browsers...');
  
  const startTime = Date.now();

  // Run tests sequentially to avoid resource conflicts
  for (const browser of browsers) {
    const result = await runTestsForBrowser(browser);
    results.push(result);
  }

  const totalDuration = Date.now() - startTime;
  const allPassed = results.every(r => r.success);
  const totalScenarios = results.reduce((sum, r) => sum + r.scenarios, 0);
  const totalPassed = results.reduce((sum, r) => sum + r.passed, 0);

  logger.info('🏁 Multi-browser test execution completed');
  logger.info(`📊 Total duration: ${(totalDuration / 1000).toFixed(1)}s`);
  logger.info(`📈 Results: ${totalPassed}/${totalScenarios} scenarios passed`);

  if (allPassed) {
    logger.info('🎉 ALL BROWSERS PASSED! 🎉');
  } else {
    logger.error('❌ Some browsers failed');
    results.filter(r => !r.success).forEach(r => {
      logger.error(`  - ${r.browser}: ${r.failed} failed scenarios`);
    });
  }

  await generateReport(results);

  process.exit(allPassed ? 0 : 1);
}

if (require.main === module) {
  main().catch(error => {
    logger.error('Fatal error:', error);
    process.exit(1);
  });
}

export { runTestsForBrowser, TestResult };
