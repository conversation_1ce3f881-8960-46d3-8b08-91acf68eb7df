import { Page, Locator } from '@playwright/test';
import { BasePage } from './BasePage';
import { logger } from '../utils/logger';

export class FormShareModal extends BasePage {
  private readonly modal: Locator;
  private readonly modalTitle: Locator;
  private readonly shareUrlInput: Locator;
  private readonly copyLinkButton: Locator;
  private readonly qrCodeImage: Locator;
  private readonly closeButton: Locator;
  private readonly embedCodeTextarea: Locator;
  private readonly copyEmbedButton: Locator;

  constructor(page: Page) {
    super(page);
    
    this.modal = page.getByRole('dialog').or(page.locator('.modal, .share-modal'));
    this.modalTitle = this.modal.getByRole('heading', { name: /共有|share/i });
    this.shareUrlInput = this.modal.getByRole('textbox', { name: /URL|リンク/i });
    this.copyLinkButton = this.modal.getByRole('button', { name: /コピー|copy/i });
    this.qrCodeImage = this.modal.locator('img[alt*="QR"], .qr-code');
    this.closeButton = this.modal.getByRole('button', { name: /閉じる|close/i });
    this.embedCodeTextarea = this.modal.getByRole('textbox', { name: /埋め込み|embed/i });
    this.copyEmbedButton = this.modal.getByRole('button', { name: /埋め込みコード|embed.*copy/i });
  }

  /**
   * Open share modal for a specific form
   */
  async open(formName: string): Promise<void> {
    logger.info(`📤 Opening share modal for form: ${formName}`);
    
    // Find the form row and click share button
    const formRow = this.page.getByRole('row').filter({ hasText: formName });
    const shareButton = formRow.getByRole('button', { name: /共有|share/i });
    
    await this.clickElement(shareButton);
    
    // Wait for modal to appear
    await this.modal.waitFor({ state: 'visible', timeout: 10000 });
    logger.info(`✅ Share modal opened for form: ${formName}`);
  }

  /**
   * Copy the share link
   */
  async copyLink(): Promise<string> {
    logger.info('📋 Copying share link');
    
    // Get the URL before copying
    const shareUrl = await this.getShareUrl();
    
    // Click copy button
    await this.clickElement(this.copyLinkButton);
    
    // Verify clipboard content (if possible)
    try {
      const clipboardText = await this.page.evaluate(() => navigator.clipboard.readText());
      if (clipboardText === shareUrl) {
        logger.info('✅ Share link copied to clipboard successfully');
      }
    } catch (error) {
      logger.warn('⚠️ Could not verify clipboard content', error);
    }
    
    logger.info(`✅ Share link copied: ${shareUrl}`);
    return shareUrl;
  }

  /**
   * Get the share URL
   */
  async getShareUrl(): Promise<string> {
    await this.waitForElement(this.shareUrlInput);
    const shareUrl = await this.shareUrlInput.inputValue();
    logger.info(`🔗 Share URL: ${shareUrl}`);
    return shareUrl;
  }

  /**
   * Copy embed code
   */
  async copyEmbedCode(): Promise<string> {
    logger.info('📋 Copying embed code');
    
    // Get the embed code before copying
    const embedCode = await this.getEmbedCode();
    
    // Click copy embed button
    await this.clickElement(this.copyEmbedButton);
    
    logger.info('✅ Embed code copied');
    return embedCode;
  }

  /**
   * Get the embed code
   */
  async getEmbedCode(): Promise<string> {
    if (await this.embedCodeTextarea.isVisible()) {
      const embedCode = await this.embedCodeTextarea.inputValue();
      logger.info(`📝 Embed code: ${embedCode}`);
      return embedCode;
    }
    return '';
  }

  /**
   * Close the modal
   */
  async close(): Promise<void> {
    logger.info('❌ Closing share modal');
    await this.clickElement(this.closeButton);
    
    // Wait for modal to disappear
    await this.modal.waitFor({ state: 'hidden', timeout: 5000 });
    logger.info('✅ Share modal closed');
  }

  /**
   * Share on social media
   */
  async shareOnSocial(platform: 'Twitter' | 'Facebook' | 'LINE'): Promise<void> {
    logger.info(`📱 Sharing on ${platform}`);
    
    const socialButton = this.modal.getByRole('button', { name: new RegExp(platform, 'i') });
    await this.clickElement(socialButton);
    
    logger.info(`✅ Shared on ${platform}`);
  }

  /**
   * Verify modal is open
   */
  async verifyModalOpen(): Promise<void> {
    logger.info('🔍 Verifying share modal is open');
    
    await Promise.all([
      this.verifyElementVisible(this.modal),
      this.verifyElementVisible(this.modalTitle),
      this.verifyElementVisible(this.shareUrlInput),
      this.verifyElementVisible(this.copyLinkButton)
    ]);
    
    logger.info('✅ Share modal verification complete');
  }

  /**
   * Verify QR code is present
   */
  async verifyQRCode(): Promise<void> {
    logger.info('🔍 Verifying QR code is present');
    
    if (await this.qrCodeImage.isVisible()) {
      await this.verifyElementVisible(this.qrCodeImage);
      logger.info('✅ QR code is present');
    } else {
      logger.info('ℹ️ QR code not available');
    }
  }

  /**
   * Verify share URL is valid
   */
  async verifyShareUrlValid(): Promise<boolean> {
    const shareUrl = await this.getShareUrl();
    
    // Basic URL validation
    try {
      new URL(shareUrl);
      logger.info('✅ Share URL is valid');
      return true;
    } catch (error) {
      logger.error('❌ Share URL is invalid', error);
      return false;
    }
  }

  /**
   * Test share URL accessibility
   */
  async testShareUrlAccessibility(): Promise<boolean> {
    const shareUrl = await this.getShareUrl();
    
    try {
      // Make a HEAD request to check if URL is accessible
      const response = await this.page.request.head(shareUrl);
      const isAccessible = response.status() === 200;
      
      if (isAccessible) {
        logger.info('✅ Share URL is accessible');
      } else {
        logger.warn(`⚠️ Share URL returned status: ${response.status()}`);
      }
      
      return isAccessible;
    } catch (error) {
      logger.error('❌ Failed to test share URL accessibility', error);
      return false;
    }
  }

  /**
   * Get modal state
   */
  async isModalOpen(): Promise<boolean> {
    try {
      await this.modal.waitFor({ state: 'visible', timeout: 1000 });
      return true;
    } catch {
      return false;
    }
  }
}
