# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Test results and reports
test-results/
reports/
logs/
*.log

# Authentication states
.auth/

# Screenshots and videos
screenshots/
videos/
traces/

# Coverage reports
coverage/
.nyc_output/

# Build outputs
dist/
build/
out/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
.cache/

# Playwright
/test-results/
/playwright-report/
/playwright/.cache/

# Allure
allure-results/
allure-report/

# Package manager
package-lock.json
yarn.lock
pnpm-lock.yaml

# TypeScript
*.tsbuildinfo

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Local development
local/
dev/
*.local

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# Database files (if any)
*.db
*.sqlite
*.sqlite3

# Certificate files
*.pem
*.key
*.crt

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes
*.kubeconfig
