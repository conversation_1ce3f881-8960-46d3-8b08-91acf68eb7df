# Environment Configuration
NODE_ENV=development

# Base URLs for different environments
BASE_URL=https://smoothcontact-web.bindec-app-stage.web-life.co.jp
DEV_BASE_URL=http://localhost:3000
STAGING_BASE_URL=https://smoothcontact-web.bindec-app-stage.web-life.co.jp
PROD_BASE_URL=https://smoothcontact-web.bindec-app.web-life.co.jp
SC_BASE_URL=https://smoothcontact-web.bindec-app-stage.web-life.co.jp

# Browser Configuration
HEADLESS=false
SLOW_MO=0
VIEWPORT_WIDTH=1280
VIEWPORT_HEIGHT=720

# Test Configuration
TIMEOUT=30000
RETRIES=0
WORKERS=1

# Logging Configuration
LOG_LEVEL=info

# Test Data
VALID_EMAIL=<EMAIL>
VALID_PASSWORD=vietnam5963
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=vietnam5963
TEST_ADMIN_EMAIL=<EMAIL>
TEST_ADMIN_PASSWORD=AdminPass123!

# API Configuration (if needed)
API_BASE_URL=http://localhost:3001/api
API_TIMEOUT=10000

# SmoothContact API Configuration
SC_API_TOKEN=your_api_token_here
SC_API_TOKEN_DEV=your_dev_api_token_here
SC_API_TOKEN_PROD=your_prod_api_token_here

# Database Configuration (if needed for test data setup)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=smoothcontact_test
DB_USER=test_user
DB_PASSWORD=test_password

# External Services (if needed)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=smtp_password

# Feature Flags (for conditional test execution)
ENABLE_EXPERIMENTAL_FEATURES=false
ENABLE_PERFORMANCE_TESTS=false
ENABLE_ACCESSIBILITY_TESTS=true

# CI/CD Configuration
CI=false
BUILD_NUMBER=local
BRANCH_NAME=main

# Report Configuration
ALLURE_RESULTS_DIR=reports/allure-results
CUCUMBER_REPORT_DIR=reports/cucumber-report
GENERATE_HTML_REPORT=true
OPEN_REPORT_AFTER_GENERATION=false
MAX_REPORTS_TO_KEEP=20

# AI Integration (Optional - Premium HTML Reports)
# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro
GEMINI_MAX_TOKENS=2048
GEMINI_TEMPERATURE=0.3

# Slack Notifications (Optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SLACK_CHANNEL=#test-results
SLACK_INCLUDE_AI_INSIGHTS=true

# Email Notifications (Optional)
EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>
EMAIL_INCLUDE_AI_INSIGHTS=true

# Screenshot and Video Settings
SCREENSHOT_ON_FAILURE=true
VIDEO_RECORDING=true
TRACE_ON_FAILURE=true

# Custom Report Styling
CUSTOM_LOGO_PATH=assets/logo.png
CUSTOM_CSS_PATH=
CUSTOM_JS_PATH=
REPORT_THEME=auto

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_THRESHOLD_MS=5000

# Forms Testing Configuration
PERF_BUDGET_PAGE_LOAD=2000
PERF_BUDGET_API_RESPONSE=1000
PERF_BUDGET_FILTER_APPLY=500
VISUAL_THRESHOLD=0.2
UPDATE_SNAPSHOTS=false
A11Y_LEVEL=AA
A11Y_TAGS=wcag2a,wcag2aa,wcag21aa

# Debugging
DEBUG_MODE=false
VERBOSE_LOGGING=false
SAVE_DEBUG_ARTIFACTS=true
