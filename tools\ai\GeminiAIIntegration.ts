// import * as fs from 'fs';
// import * as path from 'path';
import { logger } from './logger';
import { TestResult, AIInsight, AIAnalysis } from './ReportTypes';

/**
 * Gemini AI Integration for Test Report Analysis
 * 
 * Provides AI-powered insights for:
 * - Failure analysis and root cause detection
 * - Pattern recognition across test failures
 * - Debugging recommendations
 * - Test optimization suggestions
 */
export class GeminiAIIntegration {
  private apiKey: string;
  private apiEndpoint: string;
  private model: string;
  private maxTokens: number;
  private temperature: number;

  constructor(config: {
    apiKey?: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
  } = {}) {
    this.apiKey = config.apiKey || process.env.GEMINI_API_KEY || '';
    this.apiEndpoint = 'https://generativelanguage.googleapis.com/v1beta/models';
    this.model = config.model || 'gemini-pro';
    this.maxTokens = config.maxTokens || 2048;
    this.temperature = config.temperature || 0.3;
  }

  /**
   * Check if AI integration is properly configured
   */
  public isConfigured(): boolean {
    return !!this.apiKey;
  }

  /**
   * Analyze test results and generate AI insights
   */
  public async analyzeTestResults(tests: TestResult[]): Promise<AIAnalysis> {
    if (!this.isConfigured()) {
      logger.warn('Gemini AI not configured, skipping analysis');
      return this.createEmptyAnalysis();
    }

    try {
      logger.info('🧠 Starting AI analysis of test results...');
      const startTime = Date.now();

      const failedTests = tests.filter(test => test.status === 'failed');
      const flakyTests = tests.filter(test => test.status === 'flaky');
      
      const globalInsights = await this.generateGlobalInsights(tests, failedTests, flakyTests);
      const testInsights = await this.generateTestSpecificInsights(failedTests);

      const processingTime = Date.now() - startTime;
      
      logger.info(`✅ AI analysis completed in ${processingTime}ms`);

      return {
        enabled: true,
        globalInsights,
        testInsights,
        summary: {
          totalInsights: globalInsights.length + Object.keys(testInsights).length,
          criticalIssues: globalInsights.filter(i => i.category === 'critical').length,
          patterns: globalInsights.filter(i => i.type === 'pattern_detection').length,
          recommendations: globalInsights.filter(i => i.type === 'recommendation').length
        },
        processingTime,
        model: this.model,
        version: '1.0.0'
      };

    } catch (error) {
      logger.error('❌ Error during AI analysis:', error);
      return this.createEmptyAnalysis();
    }
  }

  /**
   * Generate global insights across all tests
   */
  private async generateGlobalInsights(
    allTests: TestResult[], 
    failedTests: TestResult[], 
    flakyTests: TestResult[]
  ): Promise<AIInsight[]> {
    const insights: AIInsight[] = [];

    // Failure pattern analysis
    if (failedTests.length > 0) {
      const failurePatterns = await this.analyzeFailurePatterns(failedTests);
      insights.push(...failurePatterns);
    }

    // Flaky test analysis
    if (flakyTests.length > 0) {
      const flakyAnalysis = await this.analyzeFlakyTests(flakyTests);
      insights.push(...flakyAnalysis);
    }

    // Performance analysis
    const performanceInsights = await this.analyzePerformance(allTests);
    insights.push(...performanceInsights);

    // Browser compatibility analysis
    const browserInsights = await this.analyzeBrowserCompatibility(allTests);
    insights.push(...browserInsights);

    // Success pattern analysis
    const successInsights = await this.analyzeSuccessPatterns(allTests);
    insights.push(...successInsights);

    // Test optimization recommendations
    const optimizationInsights = await this.generateOptimizationRecommendations(allTests);
    insights.push(...optimizationInsights);

    return insights;
  }

  /**
   * Generate test-specific insights for failed tests
   */
  private async generateTestSpecificInsights(failedTests: TestResult[]): Promise<Record<string, AIInsight[]>> {
    const testInsights: Record<string, AIInsight[]> = {};

    for (const test of failedTests.slice(0, 10)) { // Limit to first 10 failed tests
      try {
        const insights = await this.analyzeFailedTest(test);
        if (insights.length > 0) {
          testInsights[test.id] = insights;
        }
      } catch (error) {
        logger.warn(`Failed to analyze test ${test.id}:`, error);
      }
    }

    return testInsights;
  }

  /**
   * Analyze failure patterns across multiple tests
   */
  private async analyzeFailurePatterns(failedTests: TestResult[]): Promise<AIInsight[]> {
    const errorMessages = failedTests.map(test => test.error).filter(Boolean);
    const features = Array.from(new Set(failedTests.map(test => test.feature)));
    const browsers = Array.from(new Set(failedTests.map(test => test.browser)));

    const prompt = `
You are an expert QA automation engineer analyzing test failure patterns for a SmoothContact application test suite.

CONTEXT:
- Application: SmoothContact (Japanese web application for form management)
- Test Framework: Playwright + Cucumber
- Languages: TypeScript, Japanese UI elements
- Browsers: ${browsers.join(', ')}

FAILURE DATA:
- Total Failed Tests: ${failedTests.length}
- Features Affected: ${features.join(', ')}
- Browsers Affected: ${browsers.join(', ')}

ERROR PATTERNS:
${errorMessages.slice(0, 8).map((msg, i) => `${i + 1}. ${msg}`).join('\n')}

ANALYSIS REQUIREMENTS:
Please provide a comprehensive analysis in JSON format with the following structure:

{
  "insights": [
    {
      "title": "Clear, actionable insight title",
      "description": "Detailed analysis with specific technical details",
      "confidence": 0.8-1.0,
      "category": "critical|warning|info",
      "type": "pattern_detection",
      "actionItems": [
        "Specific debugging step 1",
        "Specific debugging step 2"
      ],
      "affectedTests": ["test1", "test2"],
      "rootCause": "Technical explanation of likely cause",
      "impact": "Business/technical impact description",
      "priority": "high|medium|low"
    }
  ]
}

FOCUS AREAS:
1. Selector stability issues (ARIA labels, Japanese text)
2. Timing/synchronization problems
3. Browser-specific compatibility issues
4. Authentication/session management
5. Network/API related failures
6. UI element interaction patterns

Provide 2-4 high-quality insights with actionable recommendations.
    `;

    try {
      const response = await this.callGeminiAPI(prompt);
      return this.parseAIResponse(response, 'pattern_detection');
    } catch (error) {
      logger.warn('Failed to analyze failure patterns:', error);
      return [];
    }
  }

  /**
   * Analyze flaky tests
   */
  private async analyzeFlakyTests(flakyTests: TestResult[]): Promise<AIInsight[]> {
    const prompt = `
Analyze flaky test behavior:

Flaky Tests: ${flakyTests.length}
Test Names: ${flakyTests.map(t => t.name).join(', ')}

Common characteristics:
- Features: ${Array.from(new Set(flakyTests.map(t => t.feature))).join(', ')}
- Browsers: ${Array.from(new Set(flakyTests.map(t => t.browser))).join(', ')}
- Average Duration: ${Math.round(flakyTests.reduce((sum, t) => sum + t.duration, 0) / flakyTests.length)}ms

Provide insights on:
1. Possible causes of flakiness
2. Stabilization recommendations
3. Test improvement suggestions

Respond in JSON format.
    `;

    try {
      const response = await this.callGeminiAPI(prompt);
      return this.parseAIResponse(response, 'recommendation');
    } catch (error) {
      logger.warn('Failed to analyze flaky tests:', error);
      return [];
    }
  }

  /**
   * Analyze performance characteristics
   */
  private async analyzePerformance(tests: TestResult[]): Promise<AIInsight[]> {
    const avgDuration = tests.reduce((sum, t) => sum + t.duration, 0) / tests.length;
    const slowTests = tests.filter(t => t.duration > avgDuration * 2);

    if (slowTests.length === 0) return [];

    const prompt = `
Analyze test performance data:

Total Tests: ${tests.length}
Average Duration: ${Math.round(avgDuration)}ms
Slow Tests (>2x avg): ${slowTests.length}

Slowest tests:
${slowTests.slice(0, 5).map(t => `${t.name}: ${t.duration}ms`).join('\n')}

Provide performance optimization recommendations.
    `;

    try {
      const response = await this.callGeminiAPI(prompt);
      return this.parseAIResponse(response, 'recommendation');
    } catch (error) {
      logger.warn('Failed to analyze performance:', error);
      return [];
    }
  }

  /**
   * Analyze browser compatibility issues
   */
  private async analyzeBrowserCompatibility(tests: TestResult[]): Promise<AIInsight[]> {
    const browserResults: Record<string, { passed: number; failed: number }> = {};
    
    tests.forEach(test => {
      if (!browserResults[test.browser]) {
        browserResults[test.browser] = { passed: 0, failed: 0 };
      }
      if (test.status === 'passed') browserResults[test.browser].passed++;
      if (test.status === 'failed') browserResults[test.browser].failed++;
    });

    const browserIssues = Object.entries(browserResults)
      .filter(([_, results]) => results.failed > 0)
      .map(([browser, results]) => ({
        browser,
        failureRate: Math.round((results.failed / (results.passed + results.failed)) * 100)
      }))
      .filter(item => item.failureRate > 20); // Only browsers with >20% failure rate

    if (browserIssues.length === 0) return [];

    const prompt = `
Analyze browser compatibility issues:

Browser failure rates:
${browserIssues.map(item => `${item.browser}: ${item.failureRate}% failure rate`).join('\n')}

Identify potential browser-specific issues and compatibility recommendations.
    `;

    try {
      const response = await this.callGeminiAPI(prompt);
      return this.parseAIResponse(response, 'recommendation');
    } catch (error) {
      logger.warn('Failed to analyze browser compatibility:', error);
      return [];
    }
  }

  /**
   * Analyze individual failed test
   */
  private async analyzeFailedTest(test: TestResult): Promise<AIInsight[]> {
    const prompt = `
You are an expert QA automation engineer analyzing a specific test failure in the SmoothContact application.

TEST DETAILS:
- Test Name: ${test.name}
- Feature: ${test.feature}
- Browser: ${test.browser}
- Duration: ${test.duration}ms
- Status: ${test.status}

ERROR INFORMATION:
- Error Message: ${test.error || 'No error message available'}
- Stack Trace: ${test.stackTrace || 'No stack trace available'}

TEST EXECUTION STEPS:
${test.steps?.map((step, i) => `${i + 1}. [${step.status?.toUpperCase()}] ${step.name}`).join('\n') || 'No step details available'}

CONTEXT:
- This is a Japanese web application for form management
- Uses Playwright with TypeScript
- Common issues: Japanese text selectors, timing, authentication
- Page Object Model architecture

ANALYSIS REQUEST:
Provide a detailed failure analysis in JSON format:

{
  "insights": [
    {
      "title": "Root Cause Analysis",
      "description": "Detailed technical explanation of what went wrong",
      "confidence": 0.7-1.0,
      "category": "critical|warning|info",
      "type": "failure_analysis",
      "actionItems": [
        "Immediate fix suggestion",
        "Long-term improvement",
        "Prevention strategy"
      ],
      "debuggingSteps": [
        "Step 1: Check specific element",
        "Step 2: Verify timing",
        "Step 3: Validate data"
      ],
      "likelyFix": "Specific code change or configuration adjustment",
      "preventionTips": "How to avoid this issue in future"
    }
  ]
}

Focus on:
1. Specific technical root cause
2. Actionable debugging steps
3. Code-level fixes
4. Prevention strategies
5. Related test improvements
    `;

    try {
      const response = await this.callGeminiAPI(prompt);
      return this.parseAIResponse(response, 'failure_analysis');
    } catch (error) {
      logger.warn(`Failed to analyze test ${test.id}:`, error);
      return [];
    }
  }

  /**
   * Call Gemini API with the given prompt
   */
  private async callGeminiAPI(prompt: string): Promise<string> {
    const url = `${this.apiEndpoint}/${this.model}:generateContent?key=${this.apiKey}`;
    
    const requestBody = {
      contents: [{
        parts: [{
          text: prompt
        }]
      }],
      generationConfig: {
        temperature: this.temperature,
        maxOutputTokens: this.maxTokens,
        topP: 0.8,
        topK: 10
      }
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.candidates || data.candidates.length === 0) {
      throw new Error('No response from Gemini API');
    }

    return data.candidates[0].content.parts[0].text;
  }

  /**
   * Analyze success patterns to identify what's working well
   */
  private async analyzeSuccessPatterns(tests: TestResult[]): Promise<AIInsight[]> {
    const passedTests = tests.filter(test => test.status === 'passed');
    if (passedTests.length === 0) return [];

    const fastTests = passedTests.filter(test => test.duration < 5000); // Under 5 seconds
    const stableFeatures = this.getStableFeatures(passedTests);
    const reliableBrowsers = this.getReliableBrowsers(passedTests);

    const prompt = `
You are analyzing successful test patterns in the SmoothContact test suite to identify best practices.

SUCCESS METRICS:
- Total Passed Tests: ${passedTests.length}
- Fast Tests (<5s): ${fastTests.length}
- Stable Features: ${stableFeatures.join(', ')}
- Reliable Browsers: ${reliableBrowsers.join(', ')}

ANALYSIS REQUEST:
Identify success patterns and best practices in JSON format:

{
  "insights": [
    {
      "title": "Success Pattern Identified",
      "description": "What's working well and why",
      "confidence": 0.8-1.0,
      "category": "info",
      "type": "success_pattern",
      "actionItems": [
        "Apply this pattern to other tests",
        "Standardize this approach"
      ],
      "bestPractices": [
        "Specific technique or approach",
        "Configuration or pattern to replicate"
      ]
    }
  ]
}

Focus on:
1. Stable selector strategies
2. Effective timing patterns
3. Reliable test structures
4. Performance optimizations
5. Cross-browser compatibility approaches
    `;

    try {
      const response = await this.callGeminiAPI(prompt);
      return this.parseAIResponse(response, 'recommendation');
    } catch (error) {
      logger.warn('Failed to analyze success patterns:', error);
      return [];
    }
  }

  /**
   * Generate test optimization recommendations
   */
  private async generateOptimizationRecommendations(tests: TestResult[]): Promise<AIInsight[]> {
    const totalDuration = tests.reduce((sum, test) => sum + test.duration, 0);
    const avgDuration = totalDuration / tests.length;
    const slowTests = tests.filter(test => test.duration > avgDuration * 1.5);
    const passRate = (tests.filter(test => test.status === 'passed').length / tests.length) * 100;

    const prompt = `
You are providing optimization recommendations for the SmoothContact test suite.

PERFORMANCE METRICS:
- Total Tests: ${tests.length}
- Total Execution Time: ${Math.round(totalDuration / 1000)}s
- Average Test Duration: ${Math.round(avgDuration)}ms
- Pass Rate: ${Math.round(passRate)}%
- Slow Tests (>1.5x avg): ${slowTests.length}

OPTIMIZATION ANALYSIS:
Provide optimization recommendations in JSON format:

{
  "insights": [
    {
      "title": "Performance Optimization",
      "description": "Specific optimization opportunity",
      "confidence": 0.8-1.0,
      "category": "info",
      "type": "optimization",
      "actionItems": [
        "Specific optimization step",
        "Implementation approach"
      ],
      "expectedImpact": "Quantified improvement expectation",
      "effort": "low|medium|high"
    }
  ]
}

Focus on:
1. Test execution speed improvements
2. Parallel execution opportunities
3. Resource optimization
4. Test structure improvements
5. CI/CD pipeline enhancements
    `;

    try {
      const response = await this.callGeminiAPI(prompt);
      return this.parseAIResponse(response, 'recommendation');
    } catch (error) {
      logger.warn('Failed to generate optimization recommendations:', error);
      return [];
    }
  }

  /**
   * Get stable features (high pass rate)
   */
  private getStableFeatures(passedTests: TestResult[]): string[] {
    const featureCounts: Record<string, number> = {};
    passedTests.forEach(test => {
      featureCounts[test.feature] = (featureCounts[test.feature] || 0) + 1;
    });

    return Object.entries(featureCounts)
      .filter(([_, count]) => count >= 3) // Features with 3+ passing tests
      .map(([feature, _]) => feature);
  }

  /**
   * Get reliable browsers (high pass rate)
   */
  private getReliableBrowsers(passedTests: TestResult[]): string[] {
    const browserCounts: Record<string, number> = {};
    passedTests.forEach(test => {
      browserCounts[test.browser] = (browserCounts[test.browser] || 0) + 1;
    });

    return Object.entries(browserCounts)
      .filter(([_, count]) => count >= 5) // Browsers with 5+ passing tests
      .map(([browser, _]) => browser);
  }

  /**
   * Parse AI response and convert to insights
   */
  private parseAIResponse(response: string, type: AIInsight['type']): AIInsight[] {
    try {
      // Try to extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        if (parsed.insights && Array.isArray(parsed.insights)) {
          return parsed.insights.map((insight: any) => ({
            type,
            title: insight.title || 'AI Insight',
            description: insight.description || insight.text || 'No description available',
            confidence: insight.confidence || 0.8,
            category: insight.category || 'info',
            actionItems: insight.actionItems || insight.recommendations || [],
            generatedAt: new Date().toISOString()
          }));
        }
      }

      // Fallback: create single insight from response text
      return [{
        type,
        title: `${type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} Insight`,
        description: response.substring(0, 500),
        confidence: 0.7,
        category: 'info',
        actionItems: [],
        generatedAt: new Date().toISOString()
      }];

    } catch (error) {
      logger.warn('Failed to parse AI response:', error);
      return [];
    }
  }

  /**
   * Create empty analysis structure
   */
  private createEmptyAnalysis(): AIAnalysis {
    return {
      enabled: false,
      globalInsights: [],
      testInsights: {},
      summary: {
        totalInsights: 0,
        criticalIssues: 0,
        patterns: 0,
        recommendations: 0
      }
    };
  }
}
