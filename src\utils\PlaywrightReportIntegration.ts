import { FullConfig, FullResult, Reporter, Suite, TestCase, TestResult } from '@playwright/test/reporter';
import { HtmlReportGeneratorScript } from '../../tools/scripts/generate-html-report';
import { logger } from './logger';

/**
 * Custom Playwright Reporter for HTML Report Generation
 * 
 * This reporter integrates with <PERSON><PERSON>'s test runner to automatically
 * generate HTML reports after test execution.
 */
export class CustomHtmlReporter implements Reporter {
  private config: FullConfig | undefined;
  private generateOnEnd: boolean;
  private openAfterGeneration: boolean;

  constructor(options: { generateOnEnd?: boolean; openAfterGeneration?: boolean } = {}) {
    this.generateOnEnd = options.generateOnEnd ?? true;
    this.openAfterGeneration = options.openAfterGeneration ?? false;
  }

  onBegin(config: FullConfig, suite: Suite) {
    this.config = config;
    logger.info('🎬 Playwright test execution started');
    logger.info(`Running ${suite.allTests().length} tests in ${config.projects.length} project(s)`);
  }

  onTestBegin(test: TestCase, result: TestResult) {
    // Optional: Log test start
    if (process.env.DEBUG) {
      logger.info(`▶️ Starting: ${test.title}`);
    }
  }

  onTestEnd(test: TestCase, result: TestResult) {
    // Optional: Log test completion
    if (process.env.DEBUG) {
      const status = result.status === 'passed' ? '✅' : 
                    result.status === 'failed' ? '❌' : 
                    result.status === 'skipped' ? '⏭️' : '⚠️';
      logger.info(`${status} ${test.title} (${result.duration}ms)`);
    }
  }

  async onEnd(result: FullResult) {
    logger.info(`🏁 Test execution completed with status: ${result.status}`);
    
    if (this.generateOnEnd) {
      try {
        logger.info('🎨 Generating HTML report...');
        
        // Wait a moment for all files to be written
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const generator = new HtmlReportGeneratorScript();
        
        // Override config if needed
        if (this.openAfterGeneration) {
          generator['config'].openAfterGeneration = true;
        }
        
        const reportPath = await generator.generateReport();
        logger.info(`📊 HTML report available at: ${reportPath}`);
        
      } catch (error) {
        logger.error('❌ Failed to generate HTML report:', error);
      }
    }
  }

  onError(error: Error) {
    logger.error('💥 Playwright execution error:', error);
  }
}

/**
 * Global Setup Hook for Playwright
 */
export async function globalSetupHook() {
  logger.info('🚀 Starting global setup...');
  
  // Clean up any previous test artifacts
  try {
    const fs = require('fs');
    const path = require('path');
    
    const testResultsDir = 'test-results';
    if (fs.existsSync(testResultsDir)) {
      logger.info('🧹 Cleaning up previous test results...');
      // Keep directory but remove contents
      const files = fs.readdirSync(testResultsDir);
      for (const file of files) {
        const filePath = path.join(testResultsDir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
          fs.rmSync(filePath, { recursive: true, force: true });
        } else {
          fs.unlinkSync(filePath);
        }
      }
    }
  } catch (error) {
    logger.warn('Warning: Could not clean up previous test results:', error);
  }
  
  logger.info('✅ Global setup completed');
}

/**
 * Global Teardown Hook for Playwright
 */
export async function globalTeardownHook() {
  logger.info('🏁 Starting global teardown...');
  
  // Generate HTML report if not already generated
  if (process.env.GENERATE_HTML_REPORT !== 'false') {
    try {
      logger.info('🎨 Generating final HTML report...');
      
      const generator = new HtmlReportGeneratorScript();
      await generator.generateReport();
      
    } catch (error) {
      logger.error('❌ Failed to generate HTML report in teardown:', error);
    }
  }
  
  logger.info('✅ Global teardown completed');
}

/**
 * Utility functions for Playwright test integration
 */
export class PlaywrightTestUtils {
  
  /**
   * Attach custom metadata to test results
   */
  static async attachTestMetadata(test: any, metadata: Record<string, any>) {
    try {
      await test.info().attach('metadata', {
        body: JSON.stringify(metadata, null, 2),
        contentType: 'application/json'
      });
    } catch (error) {
      logger.warn('Could not attach test metadata:', error);
    }
  }

  /**
   * Take a screenshot with custom naming
   */
  static async takeScreenshot(page: any, name: string, test: any) {
    try {
      const screenshot = await page.screenshot({ 
        fullPage: true,
        type: 'png'
      });
      
      await test.info().attach(name, {
        body: screenshot,
        contentType: 'image/png'
      });
      
      return screenshot;
    } catch (error) {
      logger.error('Failed to take screenshot:', error);
      throw error;
    }
  }

  /**
   * Start video recording with custom options
   */
  static async startVideoRecording(page: any, options: any = {}) {
    try {
      // Playwright automatically handles video recording based on config
      // This is a placeholder for any custom video handling
      logger.info('Video recording started (handled by Playwright config)');
    } catch (error) {
      logger.error('Failed to start video recording:', error);
    }
  }

  /**
   * Add custom step to test trace
   */
  static async addCustomStep(test: any, stepName: string, action: () => Promise<void>) {
    try {
      await test.step(stepName, async () => {
        await action();
      });
    } catch (error) {
      logger.error(`Failed to execute step "${stepName}":`, error);
      throw error;
    }
  }

  /**
   * Mark test as flaky with reason
   */
  static markAsFlaky(test: any, reason: string) {
    try {
      test.info().annotations.push({
        type: 'flaky',
        description: reason
      });
    } catch (error) {
      logger.warn('Could not mark test as flaky:', error);
    }
  }

  /**
   * Add tags to test for better reporting
   */
  static addTags(test: any, tags: string[]) {
    try {
      tags.forEach(tag => {
        test.info().annotations.push({
          type: 'tag',
          description: tag
        });
      });
    } catch (error) {
      logger.warn('Could not add tags to test:', error);
    }
  }

  /**
   * Set test priority
   */
  static setPriority(test: any, priority: 'high' | 'medium' | 'low') {
    try {
      test.info().annotations.push({
        type: 'priority',
        description: priority
      });
    } catch (error) {
      logger.warn('Could not set test priority:', error);
    }
  }

  /**
   * Add link to external resources (requirements, bug reports, etc.)
   */
  static addLink(test: any, url: string, description?: string) {
    try {
      test.info().annotations.push({
        type: 'link',
        description: description || url
      });
      
      // Also attach as metadata for HTML report
      test.info().attach('external-link', {
        body: JSON.stringify({ url, description }, null, 2),
        contentType: 'application/json'
      });
    } catch (error) {
      logger.warn('Could not add external link:', error);
    }
  }

  /**
   * Log performance metrics
   */
  static async logPerformanceMetrics(page: any, test: any) {
    try {
      const metrics = await page.evaluate(() => {
        const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        return {
          domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
          loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
          firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime || 0,
          firstContentfulPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-contentful-paint')?.startTime || 0
        };
      });

      await test.info().attach('performance-metrics', {
        body: JSON.stringify(metrics, null, 2),
        contentType: 'application/json'
      });

      return metrics;
    } catch (error) {
      logger.warn('Could not collect performance metrics:', error);
      return null;
    }
  }
}

/**
 * Custom test fixture for enhanced HTML reporting
 */
export const htmlReportFixture = {
  htmlReportUtils: async ({}, use: any) => {
    const utils = {
      attachMetadata: async (test: any, metadata: Record<string, any>) => {
        await PlaywrightTestUtils.attachTestMetadata(test, metadata);
      },
      
      takeScreenshot: async (page: any, name: string, test: any) => {
        return await PlaywrightTestUtils.takeScreenshot(page, name, test);
      },
      
      addStep: async (test: any, stepName: string, action: () => Promise<void>) => {
        await PlaywrightTestUtils.addCustomStep(test, stepName, action);
      },
      
      markFlaky: (test: any, reason: string) => {
        PlaywrightTestUtils.markAsFlaky(test, reason);
      },
      
      addTags: (test: any, tags: string[]) => {
        PlaywrightTestUtils.addTags(test, tags);
      },
      
      setPriority: (test: any, priority: 'high' | 'medium' | 'low') => {
        PlaywrightTestUtils.setPriority(test, priority);
      },
      
      addLink: (test: any, url: string, description?: string) => {
        PlaywrightTestUtils.addLink(test, url, description);
      },
      
      logPerformance: async (page: any, test: any) => {
        return await PlaywrightTestUtils.logPerformanceMetrics(page, test);
      }
    };
    
    await use(utils);
  }
};

export default CustomHtmlReporter;

