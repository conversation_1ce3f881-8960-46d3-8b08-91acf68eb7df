{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022", "DOM"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./", "paths": {"@pages/*": ["src/pages/*"], "@fixtures/*": ["tests/fixtures/*"], "@utils/*": ["src/utils/*"], "@config/*": ["config/*"], "@types/*": ["src/types/*"], "@step-definitions/*": ["tests/step-definitions/*"]}, "types": ["node", "@types/node"]}, "include": ["**/*.ts", "**/*.js", "features/**/*.feature"], "exclude": ["node_modules", "dist", "reports", "logs", "**/*.d.ts"], "ts-node": {"require": ["tsconfig-paths/register"], "compilerOptions": {"module": "commonjs"}}}