import { expect } from '@playwright/test';

/**
 * Utility to wait for a condition to become true with polling
 * Avoids hard timeouts by retrying until condition is met
 */
export async function expectEventually(
  fn: () => Promise<boolean>, 
  timeoutMs = 8000, 
  stepMs = 250
): Promise<void> {
  const t0 = Date.now();
  let last = false;
  
  while (Date.now() - t0 < timeoutMs) {
    last = await fn().catch(() => false);
    if (last) return;
    await new Promise(r => setTimeout(r, stepMs));
  }
  
  expect(last, 'eventual condition').toBeTruthy();
}
