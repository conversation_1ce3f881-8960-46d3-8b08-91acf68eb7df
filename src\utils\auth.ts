import { Page, BrowserContext } from '@playwright/test';
import { logger } from './logger';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Login via UI using the existing login page
 */
export async function loginViaUI(page: Page, email: string, password: string): Promise<void> {
  logger.info('🔐 Performing UI login');
  
  const baseUrl = process.env.BASE_URL || process.env.STAGING_BASE_URL || 'https://smoothcontact-web.bindec-app-stage.web-life.co.jp';
  
  try {
    // Navigate to login page
    await page.goto(baseUrl);
    logger.info(`🌐 Navigated to login page: ${baseUrl}`);

    // Wait for page to load completely
    await page.waitForLoadState('networkidle', { timeout: 15000 });

    // Check if we're already on a login page or need to navigate to login
    const currentUrl = page.url();
    logger.info(`📍 Current URL: ${currentUrl}`);

    // If we're redirected to dashboard/home, we might already be logged in
    if (currentUrl.includes('/dashboard') || currentUrl.includes('/home') || currentUrl.includes('/form-builder')) {
      logger.info('✅ Already logged in - redirected to authenticated page');
      return;
    }

    // Try to find login link if not on login page
    if (!currentUrl.includes('/login') && !currentUrl.includes('login')) {
      const loginLinks = [
        'a[href*="login"]',
        'a:has-text("ログイン")',
        'a:has-text("Login")',
        'button:has-text("ログイン")',
        'button:has-text("Login")',
        '[data-testid="login-link"]'
      ];

      for (const selector of loginLinks) {
        try {
          const loginLink = page.locator(selector);
          if (await loginLink.isVisible({ timeout: 2000 })) {
            await loginLink.click();
            await page.waitForLoadState('networkidle');
            logger.info(`🔗 Clicked login link: ${selector}`);
            break;
          }
        } catch {
          continue;
        }
      }
    }

    // Wait for login form to be visible - try multiple selectors
    const emailSelectors = [
      'input[name="email"]',
      'input[type="email"]',
      'input[placeholder*="メール"]',
      'input[placeholder*="email"]',
      '#email',
      '.email-input',
      '[data-testid="email"]'
    ];

    const passwordSelectors = [
      'input[name="pwd"]',
      'input[name="password"]',
      'input[type="password"]',
      'input[placeholder*="パスワード"]',
      'input[placeholder*="password"]',
      '#password',
      '.password-input',
      '[data-testid="password"]'
    ];

    let emailInput = null;
    let passwordInput = null;

    // Try to find email input
    for (const selector of emailSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 2000 });
        emailInput = selector;
        break;
      } catch {
        continue;
      }
    }

    if (!emailInput) {
      // Debug: save page content for analysis
      const pageContent = await page.content();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fs = require('fs');
      fs.writeFileSync(`logs/login-page-debug-${timestamp}.html`, pageContent);

      logger.error('❌ Email input field not found. Page content saved for debugging.');
      logger.error(`📍 Current URL: ${page.url()}`);
      logger.error(`📄 Page title: ${await page.title()}`);

      throw new Error('Email input field not found. Available selectors tried: ' + emailSelectors.join(', '));
    }

    // Try to find password input
    for (const selector of passwordSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 2000 });
        passwordInput = selector;
        break;
      } catch {
        continue;
      }
    }

    if (!passwordInput) {
      throw new Error('Password input field not found. Available selectors tried: ' + passwordSelectors.join(', '));
    }

    // Fill login form
    await page.fill(emailInput, email);
    await page.fill(passwordInput, password);
    logger.info(`📝 Filled login form for: ${email}`);

    // Submit form - try multiple submit selectors
    const submitSelectors = [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:has-text("ログイン")',
      'button:has-text("Login")',
      'button:has-text("サインイン")',
      'button:has-text("Sign In")',
      '.login-button',
      '[data-testid="login-button"]'
    ];

    let submitted = false;
    for (const selector of submitSelectors) {
      try {
        await page.click(selector);
        submitted = true;
        logger.info(`🚀 Submitted login form using: ${selector}`);
        break;
      } catch {
        continue;
      }
    }

    if (!submitted) {
      // Fallback: try pressing Enter on password field
      await page.press(passwordInput, 'Enter');
      logger.info('🚀 Submitted login form using Enter key');
    }
    
    // Wait for successful login (redirect to dashboard/home/<USER>
    await page.waitForURL(/\/(dashboard|home|form-builder)/, { timeout: 15000 });
    logger.info('✅ Login successful - redirected to authenticated page');
    
  } catch (error) {
    logger.error('❌ UI login failed:', error);
    throw new Error(`UI login failed: ${error}`);
  }
}

/**
 * Logout via UI
 */
export async function logoutViaUI(page: Page): Promise<void> {
  logger.info('🚪 Performing UI logout');
  
  try {
    // Look for logout button/menu (adjust selectors based on actual UI)
    const logoutButton = page.locator('button:has-text("ログアウト"), button:has-text("Logout"), [data-testid="logout"]');
    
    if (await logoutButton.isVisible({ timeout: 5000 })) {
      await logoutButton.click();
      logger.info('🚪 Clicked logout button');
    } else {
      // Try user menu dropdown first
      const userMenu = page.locator('[data-testid="user-menu"], .user-menu, .profile-menu');
      if (await userMenu.isVisible({ timeout: 5000 })) {
        await userMenu.click();
        await page.locator('button:has-text("ログアウト"), button:has-text("Logout")').click();
        logger.info('🚪 Logged out via user menu');
      } else {
        logger.warn('⚠️ Logout button not found - clearing storage state instead');
      }
    }
    
    // Wait for redirect to login page
    await page.waitForURL(/\/($|login)/, { timeout: 10000 });
    logger.info('✅ Logout successful');
    
  } catch (error) {
    logger.warn('⚠️ UI logout failed, clearing storage state:', error);
    // Fallback: clear storage state
    await page.context().clearCookies();
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
  }
}

/**
 * Ensure the browser context is authenticated, using storage state for performance
 */
export async function ensureAuthenticated(context: BrowserContext, reuseKey: string): Promise<void> {
  logger.info(`🔐 Ensuring authentication for: ${reuseKey}`);
  
  const authDir = '.auth';
  const authPath = path.join(authDir, `${reuseKey}.json`);
  
  // Ensure auth directory exists
  if (!fs.existsSync(authDir)) {
    fs.mkdirSync(authDir, { recursive: true });
    logger.info('📁 Created .auth directory');
  }
  
  // Try to load existing storage state
  try {
    if (fs.existsSync(authPath)) {
      const storageState = JSON.parse(fs.readFileSync(authPath, 'utf-8'));
      
      // Add cookies from storage state
      if (storageState.cookies && storageState.cookies.length > 0) {
        await context.addCookies(storageState.cookies);
        logger.info('🍪 Loaded cookies from storage state');
      }
      
      // Set local storage
      if (storageState.origins && storageState.origins.length > 0) {
        const page = await context.newPage();
        try {
          const baseUrl = process.env.BASE_URL || process.env.STAGING_BASE_URL || 'https://smoothcontact-web.bindec-app-stage.web-life.co.jp';
          await page.goto(baseUrl);
          
          for (const origin of storageState.origins) {
            if (origin.localStorage) {
              for (const item of origin.localStorage) {
                await page.evaluate(
                  ({ key, value }) => localStorage.setItem(key, value),
                  { key: item.name, value: item.value }
                );
              }
            }
          }
          logger.info('💾 Restored localStorage from storage state');
        } finally {
          await page.close();
        }
      }
    }
  } catch (error) {
    logger.warn('⚠️ Failed to load storage state:', error);
  }
  
  // Check if we're already authenticated
  const testPage = await context.newPage();
  try {
    const baseUrl = process.env.BASE_URL || process.env.STAGING_BASE_URL || 'https://smoothcontact-web.bindec-app-stage.web-life.co.jp';
    if (!baseUrl) {
      throw new Error('Base URL not configured. Please check your environment variables.');
    }

    logger.info(`🔍 Checking authentication at: ${baseUrl}/form-builder?page=1&perPage=5`);
    await testPage.goto(`${baseUrl}/form-builder?page=1&perPage=5`);
    
    // Check for authenticated indicators
    const isAuthenticated = await testPage.getByRole('button', { name: '新しいフォームを作成' })
      .isVisible({ timeout: 5000 })
      .catch(() => false);
    
    if (isAuthenticated) {
      logger.info('✅ Already authenticated');
      return;
    }
    
    logger.info('🔐 Not authenticated - performing login');
    
    // Get credentials from environment
    const email = process.env.VALID_EMAIL || process.env.TEST_USER_EMAIL;
    const password = process.env.VALID_PASSWORD || process.env.TEST_USER_PASSWORD;
    
    if (!email || !password) {
      throw new Error('❌ Authentication credentials not found in environment variables. Please set VALID_EMAIL and VALID_PASSWORD.');
    }
    
    // Perform login
    await loginViaUI(testPage, email, password);
    
    // Save storage state for future use
    await context.storageState({ path: authPath });
    logger.info(`💾 Saved authentication state to: ${authPath}`);
    
  } catch (error) {
    logger.error('❌ Authentication failed:', error);
    throw new Error(`Authentication failed: ${error}`);
  } finally {
    await testPage.close();
  }
}

/**
 * Clear authentication state for a specific browser
 */
export async function clearAuthState(reuseKey: string): Promise<void> {
  const authPath = path.join('.auth', `${reuseKey}.json`);
  
  try {
    if (fs.existsSync(authPath)) {
      fs.unlinkSync(authPath);
      logger.info(`🗑️ Cleared auth state for: ${reuseKey}`);
    }
  } catch (error) {
    logger.warn('⚠️ Failed to clear auth state:', error);
  }
}

/**
 * Check if user is currently authenticated
 */
export async function isAuthenticated(page: Page): Promise<boolean> {
  try {
    const baseUrl = process.env.BASE_URL || process.env.STAGING_BASE_URL || 'https://smoothcontact-web.bindec-app-stage.web-life.co.jp';
    if (!baseUrl) {
      logger.error('❌ Base URL not configured for authentication check');
      return false;
    }

    logger.info(`🔍 Checking authentication at: ${baseUrl}/form-builder?page=1&perPage=5`);
    await page.goto(`${baseUrl}/form-builder?page=1&perPage=5`);
    
    // Check for authenticated indicators
    return await page.getByRole('button', { name: '新しいフォームを作成' })
      .isVisible({ timeout: 5000 })
      .catch(() => false);
  } catch {
    return false;
  }
}
