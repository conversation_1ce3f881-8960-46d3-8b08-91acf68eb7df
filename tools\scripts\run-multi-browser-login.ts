#!/usr/bin/env ts-node

/**
 * Multi-Browser Login Test Runner
 * 
 * This script runs the login tests on multiple browsers (Chromium and Firefox) 
 * in headless mode and generates a comprehensive report.
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import * as fs from 'fs';
import { logger } from '../utils/logger';

const execAsync = promisify(exec);

interface TestResult {
  browser: string;
  passed: number;
  failed: number;
  total: number;
  duration: number;
  startTime: Date;
  endTime: Date;
  status: 'passed' | 'failed';
}

class MultiBrowserTestRunner {
  private browsers: string[] = ['chromium', 'firefox'];
  private results: TestResult[] = [];
  private reportsDir = 'reports';
  private testResultsDir = 'test-results';

  constructor() {
    this.ensureDirectories();
  }

  private ensureDirectories(): void {
    [this.reportsDir, this.testResultsDir, 'logs'].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  public async runTests(): Promise<void> {
    logger.info('🚀 Starting multi-browser login tests...');
    logger.info(`🎯 Target browsers: ${this.browsers.join(', ')}`);
    
    // Clean previous results
    await this.cleanPreviousResults();
    
    // Run tests on each browser
    for (const browser of this.browsers) {
      logger.info(`\n🌐 Running tests on ${browser}...`);
      await this.runTestsOnBrowser(browser);
    }
    
    // Generate comprehensive report
    await this.generateReport();
    
    // Show summary
    this.showSummary();
  }

  private async cleanPreviousResults(): Promise<void> {
    try {
      // Clean previous test results
      const testResultsFiles = [
        'reports/cucumber-report.json',
        'reports/cucumber-report.html'
      ];
      
      for (const file of testResultsFiles) {
        if (fs.existsSync(file)) {
          fs.unlinkSync(file);
        }
      }
      
      logger.info('🧹 Previous test results cleaned');
    } catch (error) {
      logger.warn('Warning: Could not clean previous results', error);
    }
  }

  private async runTestsOnBrowser(browser: string): Promise<void> {
    const startTime = new Date();
    
    try {
      // Set environment variables for the test run
      const env = {
        ...process.env,
        BROWSER_NAME: browser,
        HEADLESS: 'true', // Force headless mode
        NODE_ENV: process.env.NODE_ENV || 'development'
      };
      
      logger.info(`🔧 Environment: BROWSER_NAME=${browser}, HEADLESS=true`);
      
      // Run cucumber tests with multi-browser hooks
      const command = 'npx cucumber-js --require-module ts-node/register --require step-definitions/hooks-multi-browser.ts --require step-definitions/**/*.ts --tags "@login" features/login/*.feature --format json:reports/cucumber-report-' + browser + '.json --format html:reports/cucumber-report-' + browser + '.html';
      
      logger.info(`🏃 Executing: ${command}`);
      
      const { stdout, stderr } = await execAsync(command, { 
        env,
        cwd: process.cwd(),
        maxBuffer: 1024 * 1024 * 10 // 10MB buffer
      });
      
      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();
      
      // Parse results
      const result = await this.parseTestResults(browser, startTime, endTime, duration);
      this.results.push(result);
      
      if (stdout) logger.info(`[${browser}] Output: ${stdout}`);
      if (stderr && !stderr.includes('DeprecationWarning')) {
        logger.warn(`[${browser}] Warnings: ${stderr}`);
      }
      
      logger.info(`✅ ${browser} tests completed in ${(duration / 1000).toFixed(2)}s`);
      
    } catch (error: any) {
      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();
      
      logger.error(`❌ ${browser} tests failed:`, error.message);
      
      // Still record the failed result
      const result: TestResult = {
        browser,
        passed: 0,
        failed: 0,
        total: 0,
        duration,
        startTime,
        endTime,
        status: 'failed'
      };
      
      this.results.push(result);
    }
  }

  private async parseTestResults(
    browser: string, 
    startTime: Date, 
    endTime: Date, 
    duration: number
  ): Promise<TestResult> {
    const jsonFile = `reports/cucumber-report-${browser}.json`;
    
    let passed = 0;
    let failed = 0;
    let total = 0;
    
    try {
      if (fs.existsSync(jsonFile)) {
        const jsonContent = fs.readFileSync(jsonFile, 'utf8');
        const cucumberData = JSON.parse(jsonContent);
        
        // Parse cucumber results
        cucumberData.forEach((feature: any) => {
          feature.elements?.forEach((scenario: any) => {
            if (scenario.type === 'scenario') {
              total++;
              const hasFailedStep = scenario.steps?.some((step: any) => 
                step.result?.status === 'failed'
              );
              
              if (hasFailedStep) {
                failed++;
              } else {
                passed++;
              }
            }
          });
        });
      }
    } catch (error) {
      logger.error(`Error parsing results for ${browser}:`, error);
    }
    
    return {
      browser,
      passed,
      failed,
      total,
      duration,
      startTime,
      endTime,
      status: failed === 0 ? 'passed' : 'failed'
    };
  }

  private async generateReport(): Promise<void> {
    logger.info('📊 Generating comprehensive multi-browser report...');
    
    // Combine all cucumber reports
    await this.combineCucumberReports();
    
    // Generate HTML report using our polished generator
    try {
      const { PolishedHtmlReportGenerator } = await import('../utils/PolishedHtmlReportGenerator');
      const generator = new PolishedHtmlReportGenerator(this.reportsDir);
      
      // Create combined report data
      const reportData = await this.createCombinedReportData();
      const reportPath = await generator.generateReport(reportData);
      
      logger.info(`📄 Multi-browser HTML report generated: ${reportPath}`);
    } catch (error) {
      logger.error('Error generating HTML report:', error);
    }
  }

  private async combineCucumberReports(): Promise<void> {
    try {
      const combinedData: any[] = [];
      
      for (const browser of this.browsers) {
        const jsonFile = `reports/cucumber-report-${browser}.json`;
        
        if (fs.existsSync(jsonFile)) {
          const jsonContent = fs.readFileSync(jsonFile, 'utf8');
          const browserData = JSON.parse(jsonContent);
          
          // Add browser context to each test
          browserData.forEach((feature: any) => {
            feature.elements?.forEach((scenario: any) => {
              scenario.browser = browser;
              scenario.name = `[${browser.toUpperCase()}] ${scenario.name}`;
            });
          });
          
          combinedData.push(...browserData);
        }
      }
      
      // Write combined report
      fs.writeFileSync(
        'reports/cucumber-report.json', 
        JSON.stringify(combinedData, null, 2)
      );
      
      logger.info('📋 Combined cucumber reports generated');
    } catch (error) {
      logger.error('Error combining cucumber reports:', error);
    }
  }

  private async createCombinedReportData(): Promise<any> {
    const totalPassed = this.results.reduce((sum, r) => sum + r.passed, 0);
    const totalFailed = this.results.reduce((sum, r) => sum + r.failed, 0);
    const totalTests = this.results.reduce((sum, r) => sum + r.total, 0);
    
    const earliestStart = new Date(Math.min(...this.results.map(r => r.startTime.getTime())));
    const latestEnd = new Date(Math.max(...this.results.map(r => r.endTime.getTime())));
    
    return {
      projectName: 'SmoothContact Automation - Multi-Browser Tests',
      environment: process.env.NODE_ENV || 'development',
      runDate: new Date().toISOString(),
      runStart: earliestStart.toISOString(),
      runEnd: latestEnd.toISOString(),
      totalDuration: latestEnd.getTime() - earliestStart.getTime(),
      summary: {
        total: totalTests,
        passed: totalPassed,
        failed: totalFailed,
        skipped: 0,
        flaky: 0,
        retries: 0
      },
      tests: [], // Will be populated from the combined cucumber report
      browserResults: this.results
    };
  }

  private showSummary(): void {
    logger.info('\n📊 MULTI-BROWSER TEST SUMMARY');
    logger.info('=====================================');
    
    let overallPassed = 0;
    let overallFailed = 0;
    let overallTotal = 0;
    
    this.results.forEach(result => {
      const status = result.status === 'passed' ? '✅' : '❌';
      const duration = (result.duration / 1000).toFixed(2);
      
      logger.info(`${status} ${result.browser.toUpperCase()}: ${result.passed}/${result.total} passed (${duration}s)`);
      
      overallPassed += result.passed;
      overallFailed += result.failed;
      overallTotal += result.total;
    });
    
    logger.info('=====================================');
    logger.info(`📊 OVERALL: ${overallPassed}/${overallTotal} tests passed`);
    logger.info(`✅ Passed: ${overallPassed}`);
    logger.info(`❌ Failed: ${overallFailed}`);
    
    const passRate = overallTotal > 0 ? ((overallPassed / overallTotal) * 100).toFixed(1) : '0.0';
    logger.info(`📈 Pass Rate: ${passRate}%`);
    
    logger.info('\n📁 Generated Reports:');
    logger.info('- reports/html/latest.html (Combined HTML Report)');
    this.results.forEach(result => {
      logger.info(`- reports/cucumber-report-${result.browser}.html`);
      logger.info(`- reports/cucumber-report-${result.browser}.json`);
    });
    
    logger.info('\n🎯 All tests completed successfully!');
  }
}

// Main execution
async function main() {
  try {
    const runner = new MultiBrowserTestRunner();
    await runner.runTests();
    
    console.log('\n✅ Multi-browser login tests completed successfully!');
    console.log('📁 Check reports/html/latest.html for the detailed report');
    
  } catch (error) {
    console.error('❌ Multi-browser test execution failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export default main;
