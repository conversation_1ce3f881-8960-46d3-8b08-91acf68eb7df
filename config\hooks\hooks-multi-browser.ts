import { Before, After, BeforeAll, AfterAll, Status, setDefaultTimeout } from '@cucumber/cucumber';
import { chromium, firefox, <PERSON><PERSON><PERSON>, BrowserContext, Page } from '@playwright/test';
import { TestContext } from '../utils/TestContext';
import { ensureAuthenticated } from '../utils/auth';
import { logger } from '../utils/logger';
import { FormsFactory } from '../data/factories/forms';
import * as dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Sanitize filename to prevent path issues on Windows
function safeName(input: string): string {
  // Replace characters illegal on Windows paths and also trim
  return input
    .replace(/[\\/:*?"<>|]/g, '-')   // path separators & reserved
    .replace(/\s+/g, ' ')            // collapse whitespace
    .trim();
}

// Set default timeout for all steps to 30 seconds
setDefaultTimeout(30000);

// Load environment variables
dotenv.config();

// Browser management for multi-browser testing
let browsers: Map<string, Browser> = new Map();
let currentBrowser: string;
let context: BrowserContext;
let page: Page;

// Get browser name from environment or default to chromium
const getBrowserName = (): string => {
  return process.env.BROWSER || process.env.BROWSER_NAME || 'chromium';
};

// Launch appropriate browser based on name
const launchBrowser = async (browserName: string): Promise<Browser> => {
  const headless = process.env.HEADLESS !== 'false'; // Default to headless
  const slowMo = parseInt(process.env.SLOW_MO || '0');
  const args = ['--no-sandbox', '--disable-setuid-sandbox'];

  logger.info(`🚀 Launching ${browserName} browser in ${headless ? 'headless' : 'headed'} mode`);

  switch (browserName.toLowerCase()) {
    case 'firefox':
      return await firefox.launch({
        headless,
        slowMo,
        args,
      });
    case 'webkit':
    case 'safari':
      // Note: WebKit may not be available on all systems
      const { webkit } = require('@playwright/test');
      return await webkit.launch({
        headless,
        slowMo,
        args,
      });
    case 'chromium':
    case 'chrome':
    default:
      return await chromium.launch({
        headless,
        slowMo,
        args,
      });
  }
};

BeforeAll(async () => {
  logger.info('🚀 Starting multi-browser test suite...');
  
  currentBrowser = getBrowserName();
  logger.info(`🎯 Target browser: ${currentBrowser}`);
  
  // Launch the specified browser
  const browser = await launchBrowser(currentBrowser);
  browsers.set(currentBrowser, browser);
  
  logger.info(`🌐 ${currentBrowser} browser launched successfully`);
});

Before(async (scenario) => {
  logger.scenario(`Starting scenario: ${scenario.pickle.name} on ${currentBrowser}`);
  
  const browser = browsers.get(currentBrowser);
  if (!browser) {
    throw new Error(`Browser ${currentBrowser} not initialized`);
  }
  
  // Create new browser context for each scenario with storageState if available
  const proj = (process.env.BROWSER || process.env.BROWSER_NAME || currentBrowser).toLowerCase();
  const statePath = path.resolve('.auth', `${proj}.json`);

  const contextOptions: any = {
    viewport: {
      width: parseInt(process.env.VIEWPORT_WIDTH || '1280'),
      height: parseInt(process.env.VIEWPORT_HEIGHT || '720')
    },
    ignoreHTTPSErrors: true,
    acceptDownloads: true,
    baseURL: process.env.BASE_URL || process.env.STAGING_BASE_URL || 'https://smoothcontact-web.bindec-app-stage.web-life.co.jp',
    // Enable screenshots and video recording for multi-browser testing
    recordVideo: {
      dir: `test-results/videos/${currentBrowser}/`,
      size: { width: 1280, height: 720 }
    },
  };

  // Only load storageState if it exists and this is not a login test
  const isLoginTest = scenario.pickle.tags.some(tag =>
    tag.name === '@login' ||
    tag.name === '@LGI' ||
    scenario.pickle.name.toLowerCase().includes('login') ||
    scenario.pickle.uri?.includes('login')
  );

  if (fs.existsSync(statePath) && !isLoginTest) {
    contextOptions.storageState = statePath;
    logger.info(`💾 Loading storage state from ${statePath}`);
  } else if (isLoginTest) {
    logger.info(`🔓 Skipping storage state for login test: ${scenario.pickle.name}`);
  }

  context = await browser.newContext(contextOptions);

  // Create new page
  page = await context.newPage();

  // Set up TestContext
  const testContext = TestContext.getInstance();
  testContext.setBrowser(browser);
  testContext.setContext(context);
  testContext.setPage(page);
  testContext.setTestData('browserName', currentBrowser);

  // Add console logging with browser context
  page.on('console', (msg) => {
    if (msg.type() === 'error') {
      logger.error(`[${currentBrowser}] Browser console error: ${msg.text()}`);
    } else if (msg.type() === 'warning') {
      logger.warn(`[${currentBrowser}] Browser console warning: ${msg.text()}`);
    }
  });

  // Add page error handling
  page.on('pageerror', (error) => {
    logger.error(`[${currentBrowser}] Page error: ${error.message}`);
  });

  // Add request failure logging
  page.on('requestfailed', (request) => {
    logger.warn(`[${currentBrowser}] Request failed: ${request.url()} - ${request.failure()?.errorText}`);
  });

  // Ensure authentication for scenarios that require it (except login tests)

  if (!isLoginTest) {
    try {
      await ensureAuthenticated(context, currentBrowser);
      logger.info(`🔐 Authentication ensured for scenario: ${scenario.pickle.name}`);
    } catch (error) {
      logger.warn(`⚠️ Authentication failed for scenario: ${scenario.pickle.name}, continuing without auth: ${error}`);
    }
  } else {
    logger.info(`🔓 Skipping authentication for login test: ${scenario.pickle.name}`);
  }

  logger.info(`📄 New page context created for scenario on ${currentBrowser}`);
});

After(async function (scenario) {
  const testContext = TestContext.getInstance();
  
  if (scenario.result?.status === Status.FAILED) {
    logger.error(`❌ Scenario failed: ${scenario.pickle.name} on ${currentBrowser}`);
    
    // Take screenshot on failure with browser name
    try {
      const safeScenarioName = safeName(scenario.pickle.name);
      const screenshotName = `failed-${currentBrowser}-${safeScenarioName}`;
      const screenshotPath = await testContext.takeScreenshot(screenshotName);
      logger.info(`📸 Screenshot saved: ${screenshotPath}`);

      // Attach screenshot to Cucumber report
      if (this.attach) {
        const screenshot = await page.screenshot({ fullPage: true });
        this.attach(screenshot, 'image/png');
      }
    } catch (error) {
      logger.error(`Failed to take screenshot: ${error}`);
    }
    
    // Save page HTML for debugging with browser name
    try {
      const html = await page.content();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fs = require('fs');
      const filename = `logs/failed-page-${currentBrowser}-${timestamp}.html`;
      fs.writeFileSync(filename, html);
      logger.info(`💾 Page HTML saved for debugging: ${filename}`);
    } catch (error) {
      logger.error(`Failed to save page HTML: ${error}`);
    }
  } else {
    logger.info(`✅ Scenario passed: ${scenario.pickle.name} on ${currentBrowser}`);
  }
  
  // Clean up test data (forms, responses, etc.)
  try {
    await FormsFactory.cleanup();

    // Also clean up forms created via UI (stored in TestContext)
    const { FormsListPage } = await import('../pages/forms-list.page');
    await FormsListPage.cleanupCreatedForms();
  } catch (error) {
    logger.warn(`⚠️ Cleanup error: ${error}`);
  }

  // Close page and context
  try {
    await page.close();
    await context.close();
    logger.info(`🧹 Page and context closed for ${currentBrowser}`);
  } catch (error) {
    logger.error(`Error closing page/context: ${error}`);
  }

  // Clear test context data
  testContext.clearTestData();
});

AfterAll(async () => {
  logger.info('🏁 Multi-browser test suite completed');

  // Generate premium AI-powered report
  try {
    const { PremiumHtmlReportGenerator } = await import('../../reports/generators/PremiumHtmlReportGenerator');
    const reportGenerator = new PremiumHtmlReportGenerator();

    // Check for JSON report files
    const fs = await import('fs');

    const jsonReportPaths = [
      'reports/cucumber-report.json',
      'reports/json/cucumber-forms.json'
    ];

    // Wait a moment for file system to flush
    await new Promise(resolve => setTimeout(resolve, 1000));

    for (const jsonPath of jsonReportPaths) {
      if (fs.existsSync(jsonPath)) {
        try {
          // Validate JSON file is complete and readable
          const jsonContent = fs.readFileSync(jsonPath, 'utf8');
          if (jsonContent.trim().length === 0) {
            logger.warn(`⚠️ JSON file is empty: ${jsonPath}`);
            continue;
          }

          // Test if JSON is valid
          JSON.parse(jsonContent);

          logger.info(`🎨 Generating premium AI-powered report from ${jsonPath}...`);
          logger.info(`📖 Reading Cucumber JSON from: ${jsonPath}`);
          const outputPath = jsonPath.replace('.json', '-premium.html');
          await reportGenerator.generateFromJsonFile(jsonPath, outputPath);
          logger.info(`✨ Premium report generated: ${outputPath}`);
          break;
        } catch (jsonError) {
          const errorMessage = jsonError instanceof Error ? jsonError.message : String(jsonError);
          logger.error(`Failed to generate report from JSON: ${errorMessage}`);
          logger.warn(`⚠️ Skipping invalid JSON file: ${jsonPath}`);
          continue;
        }
      }
    }
  } catch (error) {
    logger.warn('⚠️ Failed to generate premium report:', error);
  }

  // Close all browsers
  for (const [browserName, browser] of browsers.entries()) {
    if (browser) {
      await browser.close();
      logger.info(`🌐 ${browserName} browser closed`);
    }
  }

  browsers.clear();

  // Reset test context
  TestContext.reset();

  logger.info('✅ Multi-browser cleanup completed successfully');
});

// Export for use in step definitions
export { browsers, context, page, currentBrowser };


