import { z } from 'zod';

// Form field schema
export const FormFieldSchema = z.object({
  id: z.string(),
  type: z.enum(['text', 'email', 'number', 'textarea', 'select', 'radio', 'checkbox']),
  label: z.string(),
  required: z.boolean(),
  placeholder: z.string().optional(),
  options: z.array(z.string()).optional()
});

// Form settings schema
export const FormSettingsSchema = z.object({
  allowMultipleSubmissions: z.boolean(),
  requireLogin: z.boolean(),
  showProgressBar: z.boolean(),
  customCss: z.string().optional(),
  redirectUrl: z.string().url().optional(),
  emailNotifications: z.boolean()
});

// Main form schema
export const FormSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  description: z.string().optional(),
  template: z.string().optional(),
  status: z.enum(['draft', 'published', 'scheduled', 'unpublished', 'expired']),
  fields: z.array(FormFieldSchema).optional(),
  settings: FormSettingsSchema.optional(),
  publishedAt: z.string().optional(),
  scheduledAt: z.string().optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
  responsesCount: z.number().optional(),
  shareUrl: z.string().url().optional()
});

// Forms list response schema
export const FormsListSchema = z.object({
  forms: z.array(FormSchema),
  total: z.number(),
  page: z.number(),
  perPage: z.number(),
  totalPages: z.number()
});

// Form creation request schema
export const CreateFormRequestSchema = z.object({
  name: z.string().min(1, 'Form name is required'),
  description: z.string().optional(),
  template: z.string().optional(),
  fields: z.array(FormFieldSchema).optional(),
  settings: FormSettingsSchema.optional()
});

// Form update request schema
export const UpdateFormRequestSchema = z.object({
  name: z.string().min(1, 'Form name is required').optional(),
  description: z.string().optional(),
  template: z.string().optional(),
  fields: z.array(FormFieldSchema).optional(),
  settings: FormSettingsSchema.optional()
});

// Schedule publish request schema
export const SchedulePublishRequestSchema = z.object({
  publishAt: z.string().datetime('Invalid datetime format')
});

// Response count schema
export const ResponseCountSchema = z.object({
  count: z.number().min(0)
});

// Error response schema
export const ErrorResponseSchema = z.object({
  error: z.string(),
  message: z.string(),
  statusCode: z.number(),
  timestamp: z.string().optional(),
  path: z.string().optional()
});

// API response wrapper schema
export const ApiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    success: z.boolean(),
    data: dataSchema.optional(),
    error: ErrorResponseSchema.optional(),
    message: z.string().optional()
  });

// Type exports
export type FormField = z.infer<typeof FormFieldSchema>;
export type FormSettings = z.infer<typeof FormSettingsSchema>;
export type Form = z.infer<typeof FormSchema>;
export type FormsList = z.infer<typeof FormsListSchema>;
export type CreateFormRequest = z.infer<typeof CreateFormRequestSchema>;
export type UpdateFormRequest = z.infer<typeof UpdateFormRequestSchema>;
export type SchedulePublishRequest = z.infer<typeof SchedulePublishRequestSchema>;
export type ResponseCount = z.infer<typeof ResponseCountSchema>;
export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;

// Validation helpers
export const validateForm = (data: unknown): Form => {
  return FormSchema.parse(data);
};

export const validateFormsList = (data: unknown): FormsList => {
  return FormsListSchema.parse(data);
};

export const validateCreateFormRequest = (data: unknown): CreateFormRequest => {
  return CreateFormRequestSchema.parse(data);
};

export const validateUpdateFormRequest = (data: unknown): UpdateFormRequest => {
  return UpdateFormRequestSchema.parse(data);
};

export const validateSchedulePublishRequest = (data: unknown): SchedulePublishRequest => {
  return SchedulePublishRequestSchema.parse(data);
};

export const validateResponseCount = (data: unknown): ResponseCount => {
  return ResponseCountSchema.parse(data);
};

// Custom validation functions
export const isValidFormStatus = (status: string): status is Form['status'] => {
  return ['draft', 'published', 'scheduled', 'unpublished', 'expired'].includes(status);
};

export const isValidFieldType = (type: string): type is FormField['type'] => {
  return ['text', 'email', 'number', 'textarea', 'select', 'radio', 'checkbox'].includes(type);
};

// Form validation rules
export const FormValidationRules = {
  name: {
    minLength: 1,
    maxLength: 255,
    required: true
  },
  description: {
    maxLength: 1000,
    required: false
  },
  fields: {
    minCount: 0,
    maxCount: 50,
    required: false
  },
  fieldLabel: {
    minLength: 1,
    maxLength: 255,
    required: true
  },
  fieldOptions: {
    minCount: 1,
    maxCount: 20,
    required: false
  }
};

// Status transition validation
export const isValidStatusTransition = (from: Form['status'], to: Form['status']): boolean => {
  const validTransitions: Record<Form['status'], Form['status'][]> = {
    draft: ['published', 'scheduled'],
    published: ['unpublished', 'expired'],
    scheduled: ['published', 'unpublished'],
    unpublished: ['published', 'scheduled'],
    expired: ['published', 'scheduled']
  };

  return validTransitions[from]?.includes(to) ?? false;
};

// Date validation helpers
export const isValidScheduleDate = (date: string): boolean => {
  const scheduleDate = new Date(date);
  const now = new Date();
  
  // Schedule date must be in the future
  return scheduleDate > now;
};

export const isValidDateFormat = (date: string): boolean => {
  try {
    const parsed = new Date(date);
    return !isNaN(parsed.getTime()) && parsed.toISOString() === date;
  } catch {
    return false;
  }
};
