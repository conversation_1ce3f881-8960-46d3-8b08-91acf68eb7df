# 🚀 **QUICK REFERENCE GUIDE**

## 📁 **New Directory Structure**

```
src/utils/                    # Core utilities
├── index.ts                 # 🆕 Centralized exports
├── TestContext.ts           # Test execution context
├── expectEventually.ts      # Async assertion helper
├── logger.ts               # Logging functionality
├── auth.ts                 # Authentication utilities
├── TestDataManager.ts      # Test data management
├── TestDataParser.ts       # Test data parsing
├── coachmark-utils.ts      # UI coachmark handling
├── CustomMatchers.ts       # Custom test matchers
├── PlaywrightReportIntegration.ts # Playwright integration
└── ReportTypes.ts          # Type definitions

tools/ai/                    # AI utilities
└── GeminiAIIntegration.ts  # 📍 Moved from src/utils/

reports/                     # 🆕 Unified reporting system
├── ReportManager.ts        # 🆕 Main report manager
├── generators/             # Report generators
│   ├── PremiumHtmlReportGenerator.ts # 📍 Moved
│   └── NextGenReportGenerator.ts     # 📍 Moved
└── templates/              # Report templates
    ├── PremiumReportCSS.ts # 📍 Moved
    └── PremiumReportJS.ts  # 📍 Moved
```

---

## 🎯 **Quick Commands**

### **Testing**
```bash
# Core editor test
npm run test:forms:quick

# Specific test
npx cucumber-js --require-module ts-node/register --require './tests/step-definitions/**/*.ts' --tags '@ui and @editor' tests/features/forms/form-builder-editor.feature --name "FB-01"
```

### **Reporting**
```bash
# Premium HTML report (recommended)
npm run report:premium

# NextGen modern report
npm run report:nextgen

# AI-enhanced report
npm run report:ai

# Consolidated JSON
npm run report:json

# Clean old reports
npm run report:clean
```

---

## 📦 **Import Changes**

### **Before** ❌
```typescript
import { PremiumHtmlReportGenerator } from '../utils/PremiumHtmlReportGenerator';
import { TestDataParser } from '../utils/TestDataParser';
import { logger } from '../utils/logger';
```

### **After** ✅
```typescript
// Option 1: Individual imports
import { PremiumHtmlReportGenerator } from '../../reports/generators/PremiumHtmlReportGenerator';
import { TestDataParser, logger } from '../../src/utils';

// Option 2: Centralized imports (recommended)
import { 
  TestDataParser, 
  logger, 
  TestContext,
  expectEventually 
} from '../../src/utils';
```

---

## 🔧 **Report Manager Usage**

### **Basic Usage**
```typescript
import { ReportManager } from '../reports/ReportManager';

const reportManager = new ReportManager();

// Generate premium HTML report
const result = await reportManager.generateReport({
  type: 'html',
  template: 'premium',
  features: [],
  openAfterGeneration: true
});
```

### **Advanced Configuration**
```typescript
const config = {
  type: 'html' as const,
  template: 'ai-enhanced' as const,
  features: ['ai-insights', 'performance-metrics'],
  outputPath: './custom-report.html',
  openAfterGeneration: true,
  includeAI: true,
  theme: 'dark' as const
};

const result = await reportManager.generateReport(config);
```

---

## 🎨 **Available Templates**

| Template | Description | Use Case |
|----------|-------------|----------|
| `basic` | Simple, clean template | Quick reports |
| `premium` | Professional with charts | Production reports |
| `nextgen` | Modern glass morphism | Stakeholder presentations |
| `ai-enhanced` | AI insights included | Analysis and optimization |

---

## 📊 **Report Types**

| Type | Output | Description |
|------|--------|-------------|
| `html` | `.html` | Interactive HTML report |
| `nextgen` | `.html` | Modern styled report |
| `json` | `.json` | Consolidated data |
| `allure` | `.html` | Allure format (future) |
| `junit` | `.xml` | JUnit format (future) |

---

## 🚨 **Common Issues & Solutions**

### **Import Errors**
```bash
# Error: Cannot find module
# Solution: Update import paths
- import { logger } from './logger';
+ import { logger } from '../../src/utils';
```

### **Report Generation Fails**
```bash
# Error: No JSON report files found
# Solution: Run tests first to generate data
npm run test:forms:quick
npm run report:premium
```

### **TypeScript Errors**
```bash
# Error: Type issues with ReportConfig
# Solution: Use explicit types
const config: ReportConfig = {
  type: 'html' as const,
  template: 'premium' as const,
  // ...
};
```

---

## 🔍 **File Locations**

### **Moved Files**
| Old Location | New Location |
|--------------|--------------|
| `src/utils/GeminiAIIntegration.ts` | `tools/ai/GeminiAIIntegration.ts` |
| `src/utils/NextGenReportGenerator.ts` | `reports/generators/NextGenReportGenerator.ts` |
| `src/utils/PremiumHtmlReportGenerator.ts` | `reports/generators/PremiumHtmlReportGenerator.ts` |
| `src/utils/PremiumReportCSS.ts` | `reports/templates/PremiumReportCSS.ts` |
| `src/utils/PremiumReportJS.ts` | `reports/templates/PremiumReportJS.ts` |

### **Removed Files** ❌
- `src/utils/AIAnalysisEngine.ts`
- `src/utils/AutomatedInsightsGenerator.ts`
- `src/utils/GeminiAIService.ts`
- `src/utils/IntelligentTestOptimizer.ts`
- `src/utils/PredictiveAnalytics.ts`

---

## 🎯 **Best Practices**

### **Imports**
- ✅ Use centralized imports from `src/utils/index.ts`
- ✅ Use relative paths for local files
- ✅ Group imports by source (external, internal, relative)

### **Reporting**
- ✅ Use `npm run report:premium` for production
- ✅ Use `npm run report:nextgen` for presentations
- ✅ Use `npm run report:clean` regularly
- ✅ Generate reports after test runs

### **Testing**
- ✅ Run `npm run test:forms:quick` for validation
- ✅ Check specific tests with FB-01 pattern
- ✅ Maintain 100% pass rate

---

## 📞 **Need Help?**

### **Quick Checks**
1. **Tests failing?** → Check import paths
2. **Reports not generating?** → Run tests first
3. **TypeScript errors?** → Use explicit types
4. **File not found?** → Check new locations above

### **Validation Commands**
```bash
# Verify structure
npm run test:forms:quick

# Generate test report
npm run report:premium

# Check TypeScript
npm run type-check
```

---

## 🎉 **Success Indicators**

- ✅ All tests passing (9/9 scenarios)
- ✅ Reports generating successfully
- ✅ No TypeScript errors
- ✅ Clean import statements
- ✅ Organized file structure

**You're all set! 🚀**
