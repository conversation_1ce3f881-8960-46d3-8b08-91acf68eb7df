# Forms Testing Guide

This document provides comprehensive guidance for testing the SmoothContact forms functionality using our Playwright + TypeScript + Cucumber test suite.

## Overview

The forms test suite covers the complete forms management workflow including:
- Forms list page functionality
- Form creation and editing
- Publishing and sharing workflows
- API validation and data consistency
- Visual regression testing
- Accessibility compliance
- Performance monitoring

## Test Structure

### Test IDs and Scenarios

All form tests are prefixed with `FRM-XX` for easy identification:

- **FRM-01**: Page loads & key UI present (@smoke)
- **FRM-02**: Templates render and open editor
- **FRM-03**: Filters show correct status buckets
- **FRM-04**: Sorting by 更新日 toggles asc/desc
- **FRM-05**: Pagination respects perPage and navigation
- **FRM-06**: Publish/Unpublish toggles both UI and BE (@regression)
- **FRM-07**: Share link copies correct URL
- **FRM-08**: Responses count matches API
- **FRM-09**: Row actions open correct destinations
- **FRM-10**: Localization – visible JP strings (@i18n)
- **FRM-11**: Negative – network error surfaces toast (@negative)
- **FRM-12**: Accessibility smoke (axe-core) (@a11y)
- **FRM-13**: Visual baseline for list & share modal (@visual)
- **FRM-14**: Scheduled publish shows 公開予約 badge & date
- **FRM-15**: Delete form asks confirmation and removes row (@destructive)
- **FRM-16**: Deep link with query params keeps state
- **FRM-17**: Security – sharing non-published form (@security)
- **FRM-18**: Performance budget – list loads fast (@perf)

### Page Objects

#### FormsListPage (`pages/forms-list.page.ts`)
- Navigation and page loading
- Template card interactions
- Filter and sorting functionality
- Pagination controls
- Row actions (edit, analytics, share, more)
- Data extraction methods

#### FormEditorPage (`pages/form-editor.page.ts`)
- Form creation and editing
- Publishing controls
- Form ID extraction
- Navigation between editor and list

#### FormShareModal (`pages/form-share.modal.ts`)
- Share modal interactions
- URL copying and validation
- QR code verification
- Social sharing functionality

### Data Management

#### FormsFactory (`data/factories/forms.ts`)
- Form creation with different templates
- Status management (draft, published, scheduled, etc.)
- Bulk form creation for testing
- Automatic cleanup after tests

#### FormsClient (`api/clients/formsClient.ts`)
- RESTful API interactions
- Response validation with Zod schemas
- Error handling and retry logic
- Performance monitoring

## Environment Setup

### Required Environment Variables

```bash
# Base URL for the staging environment
SC_BASE_URL=https://smoothcontact-web.bindec-app-stage.web-life.co.jp

# API authentication token
SC_API_TOKEN=your_api_token_here

# Optional: Test user credentials
SC_TEST_USERNAME=<EMAIL>
SC_TEST_PASSWORD=test_password
```

### Dependencies

The forms test suite requires these additional packages:
- `zod` - API response validation
- `axe-playwright` - Accessibility testing
- `@playwright/test` - Core testing framework

## Running Tests

### All Forms Tests
```bash
npm run test:forms
```

### By Category
```bash
# Smoke tests only
npm run test:forms:smoke

# Regression tests
npm run test:forms:regression

# Visual regression tests
npm run test:forms:visual

# Accessibility tests
npm run test:forms:a11y

# Performance tests
npm run test:forms:perf
```

### Individual Test Scenarios
```bash
# Run specific test by ID
npx cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --name "FRM-01" features/forms/*.feature

# Run tests with specific tags
npx cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --tags '@smoke and @forms' features/forms/*.feature
```

## Data Seeding

### Automatic Test Data Creation

Each test scenario automatically creates and cleans up its own test data:

```typescript
// Example: Creating forms with different statuses
Given('I have created test forms with different statuses:', async function(dataTable) {
  const statusData = dataTable.hashes();
  
  for (const row of statusData) {
    const status = row.status;
    const count = parseInt(row.count, 10);
    
    // Create forms based on status requirements
    // Automatic cleanup happens in After hook
  }
});
```

### Manual Data Seeding

For development or debugging purposes:

```typescript
import { FormsFactory } from './data/factories/forms';

const factory = new FormsFactory();

// Create a published form
const form = await factory.createForm({ name: 'Test Form' });
await factory.publishForm(form.id!);

// Create multiple forms for pagination testing
await factory.createMultipleForms(15, 'published');

// Clean up when done
await factory.cleanup();
```

## Visual Testing

### Baseline Management

Visual baselines are stored in `test-results/` and should be updated when UI changes are intentional:

```bash
# Update visual baselines
npx playwright test --update-snapshots

# Run visual tests only
npm run test:forms:visual
```

### Masking Dynamic Content

Dynamic content is automatically masked in screenshots:
- Timestamps and dates
- Response counts
- User avatars
- QR codes in share modal

## Accessibility Testing

### Running A11y Checks

```bash
npm run test:forms:a11y
```

### A11y Requirements

Tests verify:
- No critical accessibility violations (axe-core)
- Proper ARIA labels and roles
- Logical focus order
- Keyboard navigation support
- Screen reader compatibility

## Performance Testing

### Performance Budgets

- **Forms list load time**: ≤ 2.0 seconds (p95)
- **Template selection**: ≤ 1.0 seconds
- **Filter application**: ≤ 0.5 seconds

### Monitoring

Performance metrics are automatically collected and attached to test reports:

```typescript
// Example performance measurement
const startTime = performance.now();
await formsListPage.goto();
const loadTime = performance.now() - startTime;

// Attached to test report
performanceMetrics.pageLoadTime = loadTime;
```

## API Testing

### Response Validation

All API responses are validated using Zod schemas:

```typescript
// Example API validation
const response = await formsClient.listForms({ page: 1, perPage: 5 });
const validatedData = FormsListSchema.parse(response);
```

### Error Handling

Tests include negative scenarios:
- Network failures (500 errors)
- Authentication failures (401/403)
- Validation errors (400)
- Rate limiting (429)

## Troubleshooting

### Common Issues

1. **API Token Expired**
   ```
   Error: API request failed: 401 Unauthorized
   ```
   Solution: Update `SC_API_TOKEN` environment variable

2. **Visual Test Failures**
   ```
   Error: Screenshot comparison failed
   ```
   Solution: Review changes and update baselines if intentional

3. **Accessibility Violations**
   ```
   Error: 2 accessibility violations found
   ```
   Solution: Fix violations or document exceptions

4. **Performance Budget Exceeded**
   ```
   Error: Load time 2.5s exceeds budget of 2.0s
   ```
   Solution: Investigate performance issues or adjust budget

### Debug Mode

Run tests with debug output:

```bash
DEBUG=pw:api npm run test:forms
```

### Test Artifacts

Failed tests automatically generate:
- Screenshots
- Videos
- Trace files
- Network logs
- Console logs

## Contributing

### Adding New Tests

1. Add scenario to `features/forms/forms-list.feature`
2. Implement step definitions in `step-definitions/forms.steps.ts`
3. Update page objects if needed
4. Add API endpoints to `FormsClient` if required
5. Update this documentation

### Code Standards

- Use TypeScript strict mode
- Follow existing naming conventions
- Add comprehensive logging
- Include error handling
- Write descriptive test names
- Use proper Gherkin syntax

### Review Checklist

- [ ] Test ID follows FRM-XX format
- [ ] Proper tags applied (@smoke, @regression, etc.)
- [ ] API validation included where applicable
- [ ] Accessibility considerations addressed
- [ ] Performance impact considered
- [ ] Visual baselines updated if needed
- [ ] Documentation updated
- [ ] Error scenarios covered

## Reporting

### Test Reports

Multiple report formats are generated:
- Cucumber HTML reports
- Allure reports
- Playwright HTML reports
- Custom premium HTML reports

### Metrics Tracking

Key metrics tracked:
- Test execution time
- API response times
- Page load performance
- Accessibility score
- Visual regression count

### Notifications

Failed tests trigger:
- Slack notifications (if configured)
- Email alerts (if configured)
- Artifact uploads to cloud storage

## Maintenance

### Regular Tasks

- Update visual baselines monthly
- Review performance budgets quarterly
- Update API schemas when backend changes
- Refresh test data periodically
- Update dependencies regularly

### Monitoring

- Track test flakiness
- Monitor performance trends
- Review accessibility compliance
- Validate API contract changes
