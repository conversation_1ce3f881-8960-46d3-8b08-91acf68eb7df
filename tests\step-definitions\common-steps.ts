import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { TestContext } from '../../src/utils/TestContext';
import { logoutViaUI, isAuthenticated } from '../../src/utils/auth';
import { FormsListPage } from '../../src/pages/forms-list.page';
import { LoginPage } from '../../src/pages/login.page';
import { logger } from '../../src/utils/logger';

const testContext = TestContext.getInstance();

// Authentication steps
Given('I am an authenticated user', async function() {
  // This is an alias for "I am logged in" to match the Form Builder feature file
  logger.info('🔐 Verifying user is authenticated');

  const testContext = TestContext.getInstance();
  const page = testContext.getPage();

  if (!page) {
    throw new Error('Page not available. Make sure hooks are properly configured.');
  }

  // First, try to navigate to a protected page to check authentication
  try {
    const baseUrl = testContext.getConfig().baseUrl;
    await page.goto(`${baseUrl}/form-builder?page=1&perPage=5`, { waitUntil: 'domcontentloaded' });

    // Wait a bit for any redirects
    await page.waitForTimeout(2000);

    // Check current URL to see if we're on an authenticated page
    const currentUrl = page.url();
    if (currentUrl.includes('/form-builder') || currentUrl.includes('/dashboard')) {
      logger.info('✅ User is already authenticated (on authenticated page)');
      return;
    }

    // If we're on login page, we need to authenticate
    if (currentUrl.includes('/login') || currentUrl === testContext.getConfig().baseUrl + '/') {
      logger.info('🔐 Not authenticated - performing UI login');
      const loginPage = new LoginPage(page);
      await loginPage.login(process.env.VALID_EMAIL!, process.env.VALID_PASSWORD!);
      logger.info('✅ Authentication completed');
      return;
    }

    logger.info('✅ User appears to be authenticated');

  } catch (error) {
    logger.error(`❌ Authentication check failed: ${error}`);
    throw error;
  }
});

Given('I am logged in', async function() {
  logger.info('🔐 Verifying user is logged in');

  // Use TestContext for multi-browser hooks compatibility
  const testContext = TestContext.getInstance();
  const page = testContext.getPage();

  if (!page) {
    throw new Error('Page not available. Make sure hooks are properly configured.');
  }

  // Always try the target page first — if we land here and see the list, we're good.
  const baseUrl = process.env.BASE_URL || process.env.STAGING_BASE_URL || 'https://smoothcontact-web.bindec-app-stage.web-life.co.jp';
  await page.goto(`${baseUrl}/form-builder?page=1&perPage=5`, { waitUntil: 'domcontentloaded' });

  const forms = new FormsListPage(page);

  // Robust page-ready (no API polling)
  await forms.waitForReady();            // uses multiple stable anchors + networkidle best-effort
  await forms.ensureCreateSectionExpanded().catch(() => {});

  // If we see the table header or tabs, we're authenticated.
  const isListVisible = await Promise.race([
    page.getByRole('columnheader', { name: 'フォーム名' }).isVisible(),
    page.getByRole('button', { name: /すべて|公開中|公開予約|非公開|公開終了/ }).isVisible(),
  ]).catch(() => false);

  if (isListVisible) {
    logger.info('✅ Already authenticated - forms list visible');
    // Store in both TestContext and World for compatibility
    testContext.setTestData('formsList', forms);
    this.formsList = forms;
    return;
  }

  // Fallback: only now, do UI login on the same page/context
  logger.info('🔐 Not authenticated - performing UI login');
  await page.goto('/', { waitUntil: 'domcontentloaded' });
  const loginPage = new LoginPage(page);
  await loginPage.login(process.env.VALID_EMAIL!, process.env.VALID_PASSWORD!);

  // Post-login, land on forms and assert visible UI anchors
  await page.goto('/form-builder?page=1&perPage=5', { waitUntil: 'domcontentloaded' });
  await forms.waitForReady();
  await expect(page.getByRole('columnheader', { name: 'フォーム名' })).toBeVisible();

  // Store in both TestContext and World for compatibility
  testContext.setTestData('formsList', forms);
  this.formsList = forms;
  logger.info('✅ Authentication completed - forms list page ready');
});

Given('I am logged out', async function() {
  logger.info('🚪 Ensuring user is logged out');

  const page = testContext.getPage();
  await logoutViaUI(page);

  logger.info('✅ User is logged out');
});

When('I log out', async function() {
  logger.info('🚪 Logging out user');

  const page = testContext.getPage();
  await logoutViaUI(page);

  logger.info('✅ User logged out successfully');
});

Then('I should be authenticated', async function() {
  logger.info('🔍 Verifying user is authenticated');

  const page = testContext.getPage();
  const authenticated = await isAuthenticated(page);

  expect(authenticated).toBe(true);
  logger.info('✅ User authentication verified');
});

Then('I should not be authenticated', async function() {
  logger.info('🔍 Verifying user is not authenticated');

  const page = testContext.getPage();
  const authenticated = await isAuthenticated(page);

  expect(authenticated).toBe(false);
  logger.info('✅ User is not authenticated as expected');
});

Then('I should see authenticated indicator', async function() {
  logger.info('🔍 Checking for authenticated indicators');

  const page = testContext.getPage();

  // Wait for page to load completely
  await page.waitForLoadState('networkidle', { timeout: 10000 });

  // Check if we're on the forms list page (which indicates successful authentication)
  const currentUrl = page.url();
  logger.info(`🌐 Current URL: ${currentUrl}`);

  // Check for forms list page indicators
  const formsPageIndicators = [
    // Forms list specific elements
    page.locator('text=フォーム名'),
    page.locator('text=ステータス'),
    page.locator('text=更新日'),
    page.locator('text=回答数'),
    page.locator('text=すべて'),
    page.locator('text=公開中'),
    page.locator('text=非公開'),
    // Action buttons
    page.locator('[aria-label="編集"]'),
    page.locator('[aria-label="分析"]'),
    page.locator('[aria-label="共有"]'),
    // Create form button
    page.getByText('新しいフォームを作成'),
    page.getByRole('button', { name: '新しいフォームを作成' }),
    // Any table or list structure
    page.locator('table'),
    page.locator('[role="table"]'),
    page.locator('.table'),
    // Generic authenticated content
    page.locator('main'),
    page.locator('[role="main"]')
  ];

  let found = false;
  let foundIndicator = '';

  for (const indicator of formsPageIndicators) {
    try {
      if (await indicator.isVisible({ timeout: 3000 })) {
        found = true;
        foundIndicator = (await indicator.textContent().catch(() => 'authenticated content')) || 'authenticated content';
        logger.info(`✅ Found authenticated indicator: ${foundIndicator}`);
        break;
      }
    } catch (error) {
      // Continue checking other indicators
    }
  }

  // If no specific indicators found, check if URL indicates successful authentication
  if (!found && (currentUrl.includes('/forms') || currentUrl.includes('/dashboard') || !currentUrl.includes('/login'))) {
    found = true;
    foundIndicator = 'Redirected away from login page';
    logger.info(`✅ Authentication confirmed by URL redirect: ${currentUrl}`);
  }

  expect(found).toBe(true);
  logger.info(`✅ Authenticated indicator confirmed: ${foundIndicator}`);
});

// Navigation steps
Given('I am on the {string} page', async (pageName: string) => {
  logger.step(`Navigating to ${pageName} page`);
  
  const page = testContext.getPage();
  const baseUrl = testContext.getConfig().baseUrl;
  
  let url: string;
  switch (pageName.toLowerCase()) {
  case 'login':
    url = `${baseUrl}/login`;
    break;
  case 'dashboard':
    url = `${baseUrl}/dashboard`;
    break;
  case 'contacts':
    url = `${baseUrl}/contacts`;
    break;
  case 'settings':
    url = `${baseUrl}/settings`;
    break;
  case 'form builder':
  case 'form-builder':
    url = `${baseUrl}/form-builder`;
    break;
  case 'form builder editor':
  case 'form-builder-editor':
    url = `${baseUrl}/form-builder/edit/test-form-id`;
    break;
  case 'home':
  case 'homepage':
    url = baseUrl;
    break;
  default:
    url = `${baseUrl}/${pageName.toLowerCase()}`;
  }
  
  await page.goto(url);
  await page.waitForLoadState('networkidle');
  logger.info(`✅ Successfully navigated to ${pageName} page`);
});

// Removed duplicate "I navigate to {string}" step - using the one in forms.steps.ts instead

// Element interaction steps
When('I click on {string}', async (elementName: string) => {
  logger.step(`Clicking on ${elementName}`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${elementName}"]`);
  
  await locator.waitFor({ state: 'visible' });
  await locator.click();
  logger.info(`✅ Clicked on ${elementName}`);
});

When('I click on the element with text {string}', async (text: string) => {
  logger.step(`Clicking on element with text: ${text}`);
  
  const page = testContext.getPage();
  const locator = page.locator(`text=${text}`);
  
  await locator.waitFor({ state: 'visible' });
  await locator.click();
  logger.info(`✅ Clicked on element with text: ${text}`);
});

When('I fill {string} with {string}', async (fieldName: string, value: string) => {
  logger.step(`Filling ${fieldName} with: ${value}`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${fieldName}"]`);
  
  await locator.waitFor({ state: 'visible' });
  await locator.clear();
  await locator.fill(value);
  logger.info(`✅ Filled ${fieldName} with: ${value}`);
});

When('I select {string} from {string}', async (optionValue: string, selectName: string) => {
  logger.step(`Selecting ${optionValue} from ${selectName}`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${selectName}"]`);
  
  await locator.waitFor({ state: 'visible' });
  await locator.selectOption(optionValue);
  logger.info(`✅ Selected ${optionValue} from ${selectName}`);
});

// Verification steps
Then('I should see {string}', async (text: string) => {
  logger.step(`Verifying text is visible: ${text}`);
  
  const page = testContext.getPage();
  const locator = page.locator(`text=${text}`);
  
  await expect(locator).toBeVisible();
  logger.assertion(`✅ Text verified: ${text}`);
});

Then('I should not see {string}', async (text: string) => {
  logger.step(`Verifying text is not visible: ${text}`);
  
  const page = testContext.getPage();
  const locator = page.locator(`text=${text}`);
  
  await expect(locator).not.toBeVisible();
  logger.assertion(`✅ Text not visible verified: ${text}`);
});

Then('the {string} element should be visible', async (elementName: string) => {
  logger.step(`Verifying ${elementName} is visible`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${elementName}"]`);
  
  await expect(locator).toBeVisible();
  logger.assertion(`✅ Element visible verified: ${elementName}`);
});

Then('the {string} element should not be visible', async (elementName: string) => {
  logger.step(`Verifying ${elementName} is not visible`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${elementName}"]`);
  
  await expect(locator).not.toBeVisible();
  logger.assertion(`✅ Element not visible verified: ${elementName}`);
});

Then('the {string} element should be enabled', async (elementName: string) => {
  logger.step(`Verifying ${elementName} is enabled`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${elementName}"]`);
  
  await expect(locator).toBeEnabled();
  logger.assertion(`✅ Element enabled verified: ${elementName}`);
});

Then('the {string} element should be disabled', async (elementName: string) => {
  logger.step(`Verifying ${elementName} is disabled`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${elementName}"]`);
  
  await expect(locator).toBeDisabled();
  logger.assertion(`✅ Element disabled verified: ${elementName}`);
});

Then('the page title should be {string}', async (expectedTitle: string) => {
  logger.step(`Verifying page title: ${expectedTitle}`);
  
  const page = testContext.getPage();
  await expect(page).toHaveTitle(expectedTitle);
  logger.assertion(`✅ Page title verified: ${expectedTitle}`);
});

Then('the current URL should contain {string}', async (urlPart: string) => {
  logger.step(`Verifying URL contains: ${urlPart}`);
  
  const page = testContext.getPage();
  await expect(page).toHaveURL(new RegExp(urlPart));
  logger.assertion(`✅ URL contains verified: ${urlPart}`);
});

// Wait steps
When('I wait for {int} seconds', async (seconds: number) => {
  logger.step(`Waiting for ${seconds} seconds`);
  
  const page = testContext.getPage();
  await page.waitForTimeout(seconds * 1000);
  logger.info(`✅ Waited for ${seconds} seconds`);
});

When('I wait for {string} to be visible', async (elementName: string) => {
  logger.step(`Waiting for ${elementName} to be visible`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${elementName}"]`);
  
  await locator.waitFor({ state: 'visible', timeout: 30000 });
  logger.info(`✅ ${elementName} is now visible`);
});

// Data management steps
Given('I store {string} as {string}', async (value: string, key: string) => {
  logger.step(`Storing value as ${key}: ${value}`);
  
  testContext.setTestData(key, value);
  logger.info(`✅ Stored ${key}: ${value}`);
});

When('I use stored value {string} for {string}', async (key: string, fieldName: string) => {
  logger.step(`Using stored value ${key} for ${fieldName}`);
  
  const value = testContext.getTestData(key);
  if (!value) {
    throw new Error(`No stored value found for key: ${key}`);
  }
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${fieldName}"]`);
  
  await locator.waitFor({ state: 'visible' });
  await locator.clear();
  await locator.fill(value);
  logger.info(`✅ Used stored value ${key} for ${fieldName}: ${value}`);
});

// Screenshot steps
When('I take a screenshot named {string}', async (screenshotName: string) => {
  logger.step(`Taking screenshot: ${screenshotName}`);
  
  await testContext.takeScreenshot(screenshotName);
  logger.info(`✅ Screenshot taken: ${screenshotName}`);
});
