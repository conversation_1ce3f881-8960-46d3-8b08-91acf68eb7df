import { Given, When, Then, setDefaultTimeout } from '@cucumber/cucumber';

// Set timeout for long-running operations
setDefaultTimeout(60 * 1000);

// FRM-07: Share link copies correct URL
Given('I have created a published test form', async function() {
  return this.pending('Share functionality not yet implemented');
});

When('I click the {string} action for the form', async function(action: string) {
  if (action === '共有') {
    return this.pending('Share functionality not yet implemented');
  }
  // Other actions handled in appropriate step files
});

Then('the share modal should open', async function() {
  return this.pending('Share functionality not yet implemented');
});

When('I click the {string} button in the share dialog', async function(buttonText: string) {
  if (buttonText === 'copy link') {
    return this.pending('Share functionality not yet implemented');
  }
  // Other buttons handled in appropriate step files
});

Then('the share URL should be copied to clipboard', async function() {
  return this.pending('Share functionality not yet implemented');
});

Then('a GET request to the share URL should return status {int}', async function(_expectedStatus: number) {
  return this.pending('Share functionality not yet implemented');
});

// FRM-09: Row actions open correct destinations - Share part
When('I click the {string} action for the form', async function(action: string) {
  if (action === '共有') {
    return this.pending('Share functionality not yet implemented');
  }
  // Other actions handled in appropriate step files
});

// FRM-13: Visual baseline for list & share modal - Share part
When('I open the share modal for a form', async function() {
  return this.pending('Share functionality not yet implemented');
});

When('I take a screenshot of the share modal', async function() {
  return this.pending('Share functionality not yet implemented');
});

Then('it should match the share modal baseline', async function() {
  return this.pending('Share functionality not yet implemented');
});

// FRM-17: Security – sharing non-published form
When('I attempt to access the share URL directly', async function() {
  return this.pending('Share functionality not yet implemented');
});

Then('I should receive a {int} Forbidden response', async function(_expectedStatus: number) {
  return this.pending('Share functionality not yet implemented');
});

Then('I should be redirected to a login page', async function() {
  return this.pending('Share functionality not yet implemented');
});
