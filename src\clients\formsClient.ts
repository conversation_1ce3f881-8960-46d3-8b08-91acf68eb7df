import { request, APIRequestContext } from '@playwright/test';
import { FormData } from '../factories/forms';
import { FormSchema, FormsListSchema } from '../schemas/forms';
import { logger } from '../utils/logger';

export interface ListFormsParams {
  page?: number;
  perPage?: number;
  status?: 'all' | 'published' | 'scheduled' | 'unpublished' | 'expired';
  sortBy?: 'name' | 'status' | 'updatedAt' | 'responses';
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

export interface FormsListResponse {
  forms: FormData[];
  total: number;
  page: number;
  perPage: number;
  totalPages: number;
}

export class FormsClient {
  private apiContext: APIRequestContext | null = null;
  private baseUrl: string;
  private apiToken: string;

  constructor() {
    // Use separate API base URL if provided, otherwise append /api to base URL
    this.baseUrl = process.env.SC_API_BASE_URL ||
                   process.env.BASE_URL ||
                   'https://smoothcontact-web.bindec-app-stage.web-life.co.jp';
    this.apiToken = process.env.SC_API_TOKEN || '';

    logger.info(`🔧 FormsClient Configuration:`);
    logger.info(`  Base URL: ${this.baseUrl}`);
    logger.info(`  Has Token: ${!!this.apiToken}`);
    logger.info(`  Token Length: ${this.apiToken.length}`);

    if (!this.apiToken) {
      logger.warn('⚠️ SC_API_TOKEN not set, API calls may fail');
    }
  }

  /**
   * Initialize API context
   */
  private async getApiContext(): Promise<APIRequestContext> {
    if (!this.apiContext) {
      this.apiContext = await request.newContext({
        baseURL: `${this.baseUrl}/api`,
        extraHTTPHeaders: {
          'Authorization': `Bearer ${this.apiToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
    }
    return this.apiContext;
  }

  /**
   * List forms with pagination and filtering
   */
  async listForms(params: ListFormsParams = {}): Promise<FormsListResponse> {
    const {
      page = 1,
      perPage = 10,
      status = 'all',
      sortBy = 'updatedAt',
      sortOrder = 'desc',
      search = ''
    } = params;

    logger.info(`📋 Listing forms: page=${page}, perPage=${perPage}, status=${status}`);

    try {
      const apiContext = await this.getApiContext();
      
      const queryParams = new URLSearchParams({
        page: page.toString(),
        perPage: perPage.toString(),
        status,
        sortBy,
        sortOrder,
        ...(search && { search })
      });

      const response = await apiContext.get(`/forms?${queryParams}`);
      
      if (!response.ok()) {
        throw new Error(`API request failed: ${response.status()} ${response.statusText()}`);
      }

      const data = await response.json();
      
      // Validate response with Zod schema
      const validatedData = FormsListSchema.parse(data);

      logger.info(`✅ Retrieved ${validatedData.forms.length} forms`);
      return validatedData as FormsListResponse;
    } catch (error) {
      logger.error('❌ Failed to list forms:', error);
      throw error;
    }
  }

  /**
   * Create a new form
   */
  async createForm(payload: Partial<FormData>): Promise<FormData> {
    logger.info(`📝 Creating form: ${payload.name}`);

    try {
      const apiContext = await this.getApiContext();
      const url = `${this.baseUrl}/forms`;

      // Detailed logging for debugging
      logger.info(`🔍 API Request Details:`);
      logger.info(`  URL: ${url}`);
      logger.info(`  Method: POST`);
      logger.info(`  Base URL: ${this.baseUrl}`);
      logger.info(`  Has Token: ${!!process.env.SC_API_TOKEN}`);
      logger.info(`  Token Prefix: ${process.env.SC_API_TOKEN?.substring(0, 10)}...`);
      logger.info(`  Payload: ${JSON.stringify(payload, null, 2)}`);

      const response = await apiContext.post('/forms', {
        data: payload
      });

      logger.info(`🔍 API Response:`);
      logger.info(`  Status: ${response.status()}`);
      logger.info(`  Status Text: ${response.statusText()}`);
      logger.info(`  Response URL: ${response.url()}`);

      if (!response.ok()) {
        const responseText = await response.text().catch(() => 'Unable to read response body');
        logger.error(`❌ API Error Details:`);
        logger.error(`  Status: ${response.status()}`);
        logger.error(`  Status Text: ${response.statusText()}`);
        logger.error(`  Error URL: ${response.url()}`);
        logger.error(`  Response Body: ${responseText}`);
        throw new Error(`API request failed: ${response.status()} ${response.statusText()}`);
      }

      const data = await response.json();

      // Validate response with Zod schema
      const validatedForm = FormSchema.parse(data);

      logger.info(`✅ Form created with ID: ${validatedForm.id}`);
      return validatedForm as FormData;
    } catch (error) {
      logger.error('❌ Failed to create form:', error);
      throw error;
    }
  }

  /**
   * Update an existing form
   */
  async updateForm(id: string, payload: Partial<FormData>): Promise<FormData> {
    logger.info(`📝 Updating form: ${id}`);

    try {
      const apiContext = await this.getApiContext();

      const response = await apiContext.put(`/forms/${id}`, {
        data: payload
      });

      if (!response.ok()) {
        throw new Error(`API request failed: ${response.status()} ${response.statusText()}`);
      }

      const data = await response.json();

      // Validate response with Zod schema
      const validatedForm = FormSchema.parse(data);

      logger.info(`✅ Form updated: ${id}`);
      return validatedForm as FormData;
    } catch (error) {
      logger.error(`❌ Failed to update form ${id}:`, error);
      throw error;
    }
  }

  /**
   * Publish a form
   */
  async publishForm(id: string): Promise<FormData> {
    logger.info(`📢 Publishing form: ${id}`);

    try {
      const apiContext = await this.getApiContext();
      
      const response = await apiContext.post(`/forms/${id}/publish`);

      if (!response.ok()) {
        throw new Error(`API request failed: ${response.status()} ${response.statusText()}`);
      }

      const data = await response.json();
      
      // Validate response with Zod schema
      const validatedForm = FormSchema.parse(data);

      logger.info(`✅ Form published: ${id}`);
      return validatedForm as FormData;
    } catch (error) {
      logger.error(`❌ Failed to publish form ${id}:`, error);
      throw error;
    }
  }

  /**
   * Unpublish a form
   */
  async unpublishForm(id: string): Promise<FormData> {
    logger.info(`📝 Unpublishing form: ${id}`);

    try {
      const apiContext = await this.getApiContext();
      
      const response = await apiContext.post(`/forms/${id}/unpublish`);

      if (!response.ok()) {
        throw new Error(`API request failed: ${response.status()} ${response.statusText()}`);
      }

      const data = await response.json();
      
      // Validate response with Zod schema
      const validatedForm = FormSchema.parse(data);

      logger.info(`✅ Form unpublished: ${id}`);
      return validatedForm as FormData;
    } catch (error) {
      logger.error(`❌ Failed to unpublish form ${id}:`, error);
      throw error;
    }
  }

  /**
   * Schedule form publication
   */
  async schedulePublish(id: string, publishAt: string): Promise<FormData> {
    logger.info(`⏰ Scheduling form publication: ${id} at ${publishAt}`);

    try {
      const apiContext = await this.getApiContext();
      
      const response = await apiContext.post(`/forms/${id}/schedule`, {
        data: { publishAt }
      });

      if (!response.ok()) {
        throw new Error(`API request failed: ${response.status()} ${response.statusText()}`);
      }

      const data = await response.json();
      
      // Validate response with Zod schema
      const validatedForm = FormSchema.parse(data);

      logger.info(`✅ Form publication scheduled: ${id}`);
      return validatedForm as FormData;
    } catch (error) {
      logger.error(`❌ Failed to schedule form publication ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a form
   */
  async deleteForm(id: string): Promise<void> {
    logger.info(`🗑️ Deleting form: ${id}`);

    try {
      const apiContext = await this.getApiContext();
      
      const response = await apiContext.delete(`/forms/${id}`);

      if (!response.ok()) {
        throw new Error(`API request failed: ${response.status()} ${response.statusText()}`);
      }

      logger.info(`✅ Form deleted: ${id}`);
    } catch (error) {
      logger.error(`❌ Failed to delete form ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get responses count for a form
   */
  async getResponsesCount(id: string): Promise<number> {
    logger.info(`📊 Getting responses count for form: ${id}`);

    try {
      const apiContext = await this.getApiContext();
      
      const response = await apiContext.get(`/forms/${id}/responses/count`);

      if (!response.ok()) {
        throw new Error(`API request failed: ${response.status()} ${response.statusText()}`);
      }

      const data = await response.json();
      const count = data.count || 0;
      
      logger.info(`✅ Form ${id} has ${count} responses`);
      return count;
    } catch (error) {
      logger.error(`❌ Failed to get responses count for form ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get form by ID
   */
  async getForm(id: string): Promise<FormData> {
    logger.info(`📋 Getting form: ${id}`);

    try {
      const apiContext = await this.getApiContext();
      
      const response = await apiContext.get(`/forms/${id}`);

      if (!response.ok()) {
        throw new Error(`API request failed: ${response.status()} ${response.statusText()}`);
      }

      const data = await response.json();
      
      // Validate response with Zod schema
      const validatedForm = FormSchema.parse(data);

      logger.info(`✅ Retrieved form: ${id}`);
      return validatedForm as FormData;
    } catch (error) {
      logger.error(`❌ Failed to get form ${id}:`, error);
      throw error;
    }
  }

  /**
   * Close API context
   */
  async dispose(): Promise<void> {
    if (this.apiContext) {
      await this.apiContext.dispose();
      this.apiContext = null;
      logger.info('✅ API context disposed');
    }
  }
}
