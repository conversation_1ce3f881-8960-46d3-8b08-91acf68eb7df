// Common types for the test framework

export interface User {
  id?: string;
  username: string;
  password: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'user' | 'guest';
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface TestScenario {
  name: string;
  tags: string[];
  status: 'passed' | 'failed' | 'skipped' | 'pending';
  duration: number;
  error?: string;
  screenshots?: string[];
}

export interface TestReport {
  summary: {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
    duration: number;
  };
  scenarios: TestScenario[];
  environment: string;
  browser: string;
  timestamp: Date;
}

export interface PageElement {
  selector: string;
  name: string;
  type: 'button' | 'input' | 'link' | 'text' | 'dropdown' | 'checkbox' | 'radio';
  required?: boolean;
  visible?: boolean;
  enabled?: boolean;
}

export interface PageObject {
  url: string;
  title: string;
  elements: { [key: string]: PageElement };
}

export interface TestEnvironment {
  name: string;
  baseUrl: string;
  apiUrl: string;
  database?: {
    host: string;
    port: number;
    name: string;
    user: string;
    password: string;
  };
  features: {
    [key: string]: boolean;
  };
}

export interface BrowserConfig {
  name: 'chromium' | 'firefox' | 'webkit';
  headless: boolean;
  viewport: {
    width: number;
    height: number;
  };
  slowMo: number;
  timeout: number;
}

export interface TestConfig {
  environment: TestEnvironment;
  browser: BrowserConfig;
  parallel: boolean;
  workers: number;
  retries: number;
  timeout: number;
  screenshots: boolean;
  videos: boolean;
  traces: boolean;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  statusCode: number;
  headers: { [key: string]: string };
}

export interface TestData {
  users: { [role: string]: User };
  urls: { [key: string]: string };
  testCases: { [key: string]: any };
  mockData: { [key: string]: any };
}

export interface LogEntry {
  timestamp: Date;
  level: 'error' | 'warn' | 'info' | 'debug';
  message: string;
  context?: any;
  scenario?: string;
  step?: string;
}

export interface ScreenshotOptions {
  fullPage?: boolean;
  clip?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  path?: string;
  quality?: number;
  type?: 'png' | 'jpeg';
}

export interface WaitOptions {
  timeout?: number;
  state?: 'attached' | 'detached' | 'visible' | 'hidden';
}

export interface ClickOptions {
  button?: 'left' | 'right' | 'middle';
  clickCount?: number;
  delay?: number;
  force?: boolean;
  modifiers?: ('Alt' | 'Control' | 'Meta' | 'Shift')[];
  position?: { x: number; y: number };
  timeout?: number;
}

export interface FillOptions {
  force?: boolean;
  noWaitAfter?: boolean;
  timeout?: number;
}

export interface SelectOptions {
  force?: boolean;
  noWaitAfter?: boolean;
  timeout?: number;
}

// Cucumber World interface
export interface World {
  attach: (data: string | Buffer, mediaType?: string) => void;
  log: (text: string) => void;
  parameters: { [key: string]: string };
}

// Custom matchers for assertions
export interface CustomMatchers<R = unknown> {
  toBeVisibleWithinTimeout(timeout?: number): R;
  toHaveTextContaining(text: string): R;
  toBeEnabledWithinTimeout(timeout?: number): R;
  toHaveAttributeValue(attribute: string, value: string): R;
}

declare global {
  namespace jest {
    interface Expect extends CustomMatchers {}
    interface Matchers<R> extends CustomMatchers<R> {}
    interface InverseAsymmetricMatchers extends CustomMatchers {}
  }
}
