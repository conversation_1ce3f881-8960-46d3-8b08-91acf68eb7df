import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';
import { logger } from '../utils/logger';

export type TemplateType = '空白のフォーム' | 'お問い合わせフォーム' | 'クイックアンケート' | '申込フォーム' | 'ご予約受付フォーム';
export type FilterType = 'すべて' | '公開中' | '公開予約' | '非公開' | '公開終了';
export type SortColumn = 'フォーム名' | 'ステータス' | '更新日' | '回答数';
export type RowAction = '編集' | 'レポート' | '共有' | 'その他';

/**
 * Page Object for the Forms List page
 * Uses robust role/aria-based selectors and handles Japanese text variants
 * Handles Joyride overlay dismissal for stable test execution
 */
export class FormsListPage extends BasePage {
  // Header / nav
  private readonly brand: Locator;

  // ---- table scope (scoped to eliminate strict-mode violations) ----
  readonly table: Locator;
  readonly tbody: Locator;
  readonly rows: Locator;

  // ---- filter tabs (scope to tablist to avoid status chips) ----
  readonly tablist: Locator;
  readonly allTab: Locator;
  readonly publishedTab: Locator;
  readonly scheduledTab: Locator;
  readonly unpublishedTab: Locator;
  readonly endedTab: Locator;

  // ---- header <th> for 更新日 (toggle reads aria-sort) ----
  readonly updatedHeaderTh: Locator;
  readonly updatedHeaderBtn: Locator;
  readonly nameHeaderBtn: Locator;

  // ---- pagination (ARIA names) ----
  readonly nextPageBtn: Locator;
  readonly prevPageBtn: Locator;
  readonly firstPageBtn: Locator;
  readonly lastPageBtn: Locator;
  readonly displayedRows: Locator;
  readonly rowsPerPageCombobox: Locator;
  readonly pageIndicator: Locator;

  // ---- dialogs / menus ----
  readonly lastDialog: Locator;
  readonly lastMenu: Locator;

  // "新しいフォームを作成" section (collapsible)
  private readonly newFormSection: Locator;

  // Template cards (use the visible card title OR image alt)
  private readonly template_Blank: Locator;
  private readonly template_Contact: Locator;
  private readonly template_Quick: Locator;
  private readonly template_Application: Locator;
  private readonly template_Reservation: Locator;

  // Table column headers (MUI renders sort labels as role=button inside <th>)
  private readonly col_Name: Locator;
  private readonly col_Status: Locator;
  private readonly col_UpdatedAt: Locator;
  private readonly col_Responses: Locator;
  readonly colUpdated: Locator;

  constructor(page: Page) {
    super(page, '/form-builder?page=1&perPage=5');
    
    // Header / nav (logo image inside link)
    this.brand = page.locator('a[href="/"] img[alt="logo"]');

    // ---- table scope (scoped to eliminate strict-mode violations) ----
    this.table = page.locator('table.MuiTable-root');
    this.tbody = this.table.locator('tbody');
    this.rows = this.tbody.locator('tr'); // data rows only

    // ---- filter tabs (scope to avoid status chips) ----
    // Use button role with text content - simpler selectors without count requirement
    this.tablist = page.locator('.MuiStack-root').first(); // Container for filter buttons
    this.allTab = page.getByRole('button').filter({ hasText: 'すべて' }).first();
    this.publishedTab = page.getByRole('button').filter({ hasText: '公開中' }).first();
    this.scheduledTab = page.getByRole('button').filter({ hasText: '公開予約' }).first();
    this.unpublishedTab = page.getByRole('button').filter({ hasText: '非公開' }).first();
    this.endedTab = page.getByRole('button').filter({ hasText: '公開終了' }).first();

    // ---- header <th> for 更新日 (toggle reads aria-sort) ----
    this.updatedHeaderTh = page.locator('th:has-text("更新日")');
    // Use the button inside the TH, not the TH itself to avoid strict mode
    this.updatedHeaderBtn = this.updatedHeaderTh.getByRole('button').first();
    this.nameHeaderBtn = page.locator('th:has-text("フォーム名") [role="button"]');

    // Pagination - using accessible names
    this.nextPageBtn = page.getByRole('button', { name: 'Go to next page' });
    this.prevPageBtn = page.getByRole('button', { name: 'Go to previous page' });
    this.firstPageBtn = page.getByRole('button', { name: 'Go to first page' });
    this.lastPageBtn = page.getByRole('button', { name: 'Go to last page' });
    this.displayedRows = page.locator('.MuiTablePagination-displayedRows');
    this.rowsPerPageCombobox = page.getByRole('combobox', { name: 'Rows per page:' });

    // ---- dialogs / menus ----
    // Try multiple selectors for dialogs (share modal might use different structure)
    this.lastDialog = page.locator('[role="dialog"], .MuiDialog-root, .MuiModal-root .MuiPaper-root').last();
    this.lastMenu = page.locator('[role="menu"]').last();

    // "新しいフォームを作成" section (collapsible)
    this.newFormSection = page.getByText('新しいフォームを作成', { exact: true });

    // Template cards (use the card containers with text labels)
    this.template_Blank = page.locator('.step-create-form').or(
      page.getByText('空白のフォーム').locator('..'));
    this.template_Contact = page.getByText('お問い合わせフォーム').locator('..');
    this.template_Quick = page.getByText('クイックアンケート').locator('..');
    this.template_Application = page.getByText('申込フォーム').locator('..');
    this.template_Reservation = page.getByText('ご予約受付フォーム').locator('..');

    // Table column headers (MUI renders sort labels as role=button inside <th>)
    this.col_Name = page.getByRole('columnheader', { name: 'フォーム名' });
    this.col_Status = page.getByRole('columnheader', { name: 'ステータス' });
    this.col_UpdatedAt = page.getByRole('columnheader', { name: '更新日' });
    this.col_Responses = page.getByRole('columnheader', { name: '回答数' });
    this.colUpdated = page.getByRole('columnheader', { name: '更新日' });

    // Pagination indicator
    this.pageIndicator = page.locator('[aria-current="page"]');
  }

  /**
   * Helper methods for row interactions
   */

  statusChip(name: string): Locator {
    return this.page.getByRole('link', { name }).locator('..').getByRole('button', { name: /公開中|非公開|公開予約|公開終了/ });
  }

  editBtn(name: string): Locator {
    return this.rowCell(name).getByRole('button', { name: '編集' });
  }

  reportBtn(name: string): Locator {
    return this.rowCell(name).getByRole('button', { name: 'レポート' });
  }

  shareBtn(name: string): Locator {
    return this.rowCell(name).getByRole('button', { name: '共有' });
  }

  moreBtn(name: string): Locator {
    return this.rowCell(name).locator('button[aria-haspopup="true"]');
  }

  rowCell(name: string): Locator {
    return this.page.getByRole('link', { name }).locator('ancestor::tr');
  }

  updatedAtText(name: string): Locator {
    return this.rowCell(name).getByRole('cell').nth(3);
  }

  responsesText(name: string): Locator {
    return this.rowCell(name).getByRole('cell').nth(4);
  }

  /**
   * Get update dates from the column as epoch milliseconds
   */
  async getUpdateDates(): Promise<number[]> {
    const rows = this.page.getByRole('row').filter({ hasNot: this.page.getByRole('columnheader') });
    const dates: number[] = [];

    const count = await rows.count();
    for (let i = 0; i < count; i++) {
      const row = rows.nth(i);
      const dateCell = row.getByRole('cell').nth(3); // 更新日 column
      const dateText = await dateCell.innerText();

      // Parse Japanese date format (e.g., "2024/01/15 10:30")
      const dateMatch = dateText.match(/(\d{4})\/(\d{1,2})\/(\d{1,2})\s+(\d{1,2}):(\d{1,2})/);
      if (dateMatch) {
        const [, year, month, day, hour, minute] = dateMatch;
        const date = new Date(
          parseInt(year),
          parseInt(month) - 1, // Month is 0-indexed
          parseInt(day),
          parseInt(hour),
          parseInt(minute)
        );
        dates.push(date.getTime());
      }
    }

    return dates;
  }

  /**
   * Dismiss the Joyride overlay (blocks clicks)
   */
  async closeJoyrideIfPresent(): Promise<void> {
    try {
      const close = this.page.locator('[data-test-id="button-close"]');
      const next = this.page.locator('[data-test-id="button-primary"]', { hasText: '次へ' });
      const beacon = this.page.locator('.react-joyride__beacon');

      if (await close.isVisible({ timeout: 1000 })) {
        await close.click();
        logger.info('🚫 Closed Joyride overlay');
      }

      while (await next.isVisible({ timeout: 1000 })) {
        await next.click();
        logger.info('➡️ Advanced Joyride step');
      }

      if (await beacon.isVisible({ timeout: 1000 })) {
        await beacon.click({ trial: true }).catch(() => {});
        logger.info('🎯 Clicked Joyride beacon');
      }
    } catch (error) {
      // Joyride not present, continue
      logger.debug('No Joyride overlay found');
    }
  }

  /**
   * Enhanced wait for forms list page to be ready
   * Waits for API completion and all template cards to be visible
   */
  async waitForReady(): Promise<void> {
    // 1) Ensure navigation finished and app hydration mostly done
    await this.page.waitForLoadState('domcontentloaded');

    // 2) Wait for critical API calls to complete
    try {
      await Promise.race([
        // Wait for forms list API
        this.page.waitForResponse(response =>
          response.url().includes('/form-builder') &&
          response.url().includes('page=1') &&
          response.url().includes('perPage=5') &&
          response.status() === 200,
          { timeout: 15000 }
        ),
        // Wait for user profile API
        this.page.waitForResponse(response =>
          response.url().includes('/me') &&
          response.status() === 200,
          { timeout: 15000 }
        ),
        // Fallback to networkidle
        this.page.waitForLoadState('networkidle', { timeout: 10000 })
      ]);
      logger.info('✅ API calls completed');
    } catch (error) {
      logger.warn('⚠️ API wait timeout, proceeding with DOM checks');
    }

    // 3) Dismiss Joyride if present (blocks clicks)
    await this.closeJoyrideIfPresent();

    // 4) Wait for core UI elements to be visible
    const coreElements = [
      // table header (very stable)
      this.page.getByRole('columnheader', { name: 'フォーム名' }),
      // tab bar (also stable)
      this.page.getByRole('button', { name: /すべて|公開中|公開予約|非公開|公開終了/ }),
    ];
    await Promise.any(coreElements.map(l => l.waitFor({ state: 'visible', timeout: 8000 })));

    // 5) Ensure create section is expanded and wait for template cards (non-blocking)
    await this.ensureCreateSectionExpanded();
    await this.waitForAllTemplateCards().catch(() => {
      logger.warn('⚠️ Template cards wait failed, proceeding anyway');
    });

    logger.info('✅ Forms list page is ready with all template cards visible');
  }

  /**
   * Returns a locator that should be the actual actionable "create new form" control
   */
  get createFormButton() {
    // Try by role-first, then by text, then a data-testid if we add one later.
    // The text can sometimes be within a button or within a heading wrapping a button.
    const roleBtn = this.page.getByRole('button', { name: /新しいフォーム.*作成/ });
    const textBtn = this.page.getByText(/新しいフォーム.*作成/).locator('xpath=ancestor-or-self::button[1]');
    const testId = this.page.locator('[data-testid="create-form"], [data-test="create-form"]');
    return roleBtn.or(textBtn).or(testId);
  }

  /**
   * The section may be collapsed; expand it if needed
   */
  async ensureCreateSectionExpanded(): Promise<void> {
    const sectionLabel = this.page.getByText('新しいフォームを作成', { exact: false });
    // If the create button isn't visible, try toggling the section
    if (!(await this.createFormButton.isVisible().catch(() => false))) {
      if (await sectionLabel.isVisible().catch(() => false)) {
        await sectionLabel.click({ trial: true }).catch(() => {});
        await sectionLabel.click().catch(() => {});
      }
    }
  }

  /**
   * Wait for all template cards to be visible
   * Uses more specific selectors to avoid strict mode violations
   */
  async waitForAllTemplateCards(): Promise<void> {
    try {
      // Wait for the template section to be visible first
      await this.page.getByText('新しいフォームを作成', { exact: false }).waitFor({ state: 'visible', timeout: 5000 });

      // Wait for specific template cards using more targeted selectors
      const templateSelectors = [
        // Blank form - use the specific create form button
        this.page.locator('.step-create-form').first(),
        // Other templates - use role-based selectors within the template section
        this.page.getByRole('button', { name: /お問い合わせフォーム/ }).or(this.page.getByText('お問い合わせフォーム').first()),
        this.page.getByRole('button', { name: /クイックアンケート/ }).or(this.page.getByText('クイックアンケート').first()),
        this.page.getByRole('button', { name: /申込フォーム/ }).or(this.page.getByText('申込フォーム').first()),
        this.page.getByRole('button', { name: /ご予約受付フォーム/ }).or(this.page.getByText('ご予約受付フォーム').first())
      ];

      // Wait for at least 3 template cards to be visible (more lenient)
      let visibleCount = 0;
      for (const selector of templateSelectors) {
        try {
          await selector.waitFor({ state: 'visible', timeout: 2000 });
          visibleCount++;
        } catch (error) {
          // Continue if one template card is not visible
          logger.debug(`Template card not visible: ${error}`);
        }
      }

      if (visibleCount >= 3) {
        logger.info(`✅ ${visibleCount} template cards are visible`);
      } else {
        logger.warn(`⚠️ Only ${visibleCount} template cards visible, proceeding anyway`);
      }
    } catch (error) {
      logger.warn(`⚠️ Template cards wait failed: ${error}, proceeding anyway`);
    }
  }

  /**
   * Navigate to forms list page with optional parameters
   */
  async gotoList(params?: { page?: number; perPage?: number }): Promise<void> {
    const page = params?.page || 1;
    const perPage = params?.perPage || 5;
    await this.page.goto(`/form-builder?page=${page}&perPage=${perPage}`);
    await this.waitForReady();
  }

  /**
   * Navigate to forms list page (legacy method for backward compatibility)
   */
  async goto(page = 1, perPage = 5): Promise<void> {
    await this.gotoList({ page, perPage });
  }

  /**
   * Check if page is loaded (alias for isReady)
   */
  async isLoaded(): Promise<void> {
    // single, consistent guard used by steps
    await this.waitForReady();
  }

  /**
   * Check if page is ready (more semantic name)
   */
  async isReady(): Promise<void> {
    await this.waitForReady();
  }

  /**
   * Click template card by name
   */
  async clickTemplate(name: "空白のフォーム" | "お問い合わせフォーム" | "クイックアンケート" | "申込フォーム" | "ご予約受付フォーム"): Promise<string | null> {
    return await this.openTemplate(name);
  }

  /**
   * Filter forms by tab
   */
  async filterBy(tab: 'すべて' | '公開中' | '公開予約' | '非公開' | '公開終了'): Promise<void> {
    // Use hardened selectors for filter tabs
    const tabSelectors = {
      'すべて': this.allTab,
      '公開中': this.publishedTab,
      '公開予約': this.scheduledTab,
      '非公開': this.unpublishedTab,
      '公開終了': this.endedTab
    };

    const tabButton = tabSelectors[tab];
    if (!tabButton) {
      throw new Error(`Unknown tab: ${tab}`);
    }

    await tabButton.click();
    await this.page.waitForLoadState('networkidle');

    // Ensure rows are visible after filtering
    await expect.poll(async () => await this.rows.count()).toBeGreaterThan(0);

    logger.info(`✅ Filtered by: ${tab}`);
  }

  /**
   * Click filter tab (more semantic name)
   */
  async clickFilter(label: "公開中" | "非公開" | "公開予約" | "すべて"): Promise<void> {
    await this.filterBy(label);
  }

  /**
   * Sort table by column
   */
  async sortBy(column: 'フォーム名' | 'ステータス' | '更新日' | '回答数', direction: 'asc' | 'desc' = 'asc'): Promise<void> {
    const header = this.page.getByRole('columnheader', { name: column });
    // MUI toggles on click; click until aria-sort matches
    for (let i = 0; i < 3; i++) {
      const sort = await header.getAttribute('aria-sort');
      if ((direction === 'asc' && sort === 'ascending') || (direction === 'desc' && sort === 'descending')) break;
      await header.getByRole('button').click();
    }
    logger.info(`✅ Sorted by ${column} (${direction})`);
  }

  /**
   * Toggle sort by updated date
   */
  async toggleSortByUpdatedAt(): Promise<void> {
    const header = this.page.getByRole('columnheader', { name: '更新日' });
    await header.getByRole('button').click();
    logger.info('✅ Toggled sort by updated date');
  }

  /**
   * Expect forms to be sorted by updated date in specified order
   */
  async expectSortedByUpdatedAt(order: "asc" | "desc"): Promise<void> {
    const header = this.page.getByRole('columnheader', { name: '更新日' });
    const sortAttribute = await header.getAttribute('aria-sort');
    const expectedSort = order === 'asc' ? 'ascending' : 'descending';
    expect(sortAttribute).toBe(expectedSort);
    logger.info(`✅ Confirmed sort order: ${order}`);
  }

  /**
   * Open template by name with API interception for form ID capture
   */
  async openTemplate(name: '空白のフォーム' | 'お問い合わせフォーム' | 'クイックアンケート' | '申込フォーム' | 'ご予約受付フォーム'): Promise<string | null> {
    logger.info(`🖱️ Opening template: ${name}`);

    // Enhanced reliability strategy with multiple approaches
    const strategies = [
      {
        name: 'API Interception with Resilient Click',
        execute: async () => {
          logger.info('🎯 Strategy 1: API Interception with enhanced clicking');

          // Set up API interception with shorter timeout for faster fallback
          const apiPromise = this.page.waitForResponse(response =>
            response.url().includes('create-empty') &&
            response.request().method() === 'POST' &&
            response.status() === 201,
            { timeout: 15000 }
          );

          // Enhanced template clicking with multiple selectors
          await this.clickTemplateCardResilient(name);

          // Wait for API response
          const createResponse = await apiPromise;
          const responseData = await createResponse.json();
          const formId = responseData.id || responseData.data?.id;

          if (formId) {
            logger.info(`✅ Form created with ID: ${formId}`);
            await this.storeFormIdForCleanup(formId);
            return formId;
          }

          return null;
        }
      },
      {
        name: 'Direct Click with URL Monitoring',
        execute: async () => {
          logger.info('🎯 Strategy 2: Direct click with URL monitoring');

          // Monitor URL changes to detect successful navigation
          const urlChangePromise = this.page.waitForURL(/\/form-builder\/edit\//, { timeout: 30000 });

          // Click template with enhanced reliability
          await this.clickTemplateCardResilient(name);

          // Wait for navigation to editor
          await urlChangePromise;

          // Extract form ID from URL if possible
          const currentUrl = this.page.url();
          const urlMatch = currentUrl.match(/\/form-builder\/edit\/([^/?#]+)/);
          const formId = urlMatch ? urlMatch[1] : null;

          if (formId) {
            logger.info(`✅ Form created with ID from URL: ${formId}`);
            await this.storeFormIdForCleanup(formId);
            return formId;
          }

          logger.info('✅ Successfully navigated to Form Builder editor (ID from URL not available)');
          return null;
        }
      },
      {
        name: 'Fallback Click Only',
        execute: async () => {
          logger.info('🎯 Strategy 3: Fallback click with basic validation');

          await this.clickTemplateCardResilient(name);
          await this.page.waitForLoadState('networkidle');

          const currentUrl = this.page.url();
          if (currentUrl.includes('/form-builder/edit/')) {
            logger.info('✅ Successfully navigated to Form Builder editor (fallback)');
            return null;
          }

          throw new Error(`Failed to navigate to editor. Current URL: ${currentUrl}`);
        }
      }
    ];

    // Try strategies in order until one succeeds
    for (const strategy of strategies) {
      try {
        logger.info(`🚀 Attempting: ${strategy.name}`);
        const result = await strategy.execute();
        logger.info(`✅ ${strategy.name} succeeded`);
        return result;
      } catch (error) {
        logger.warn(`⚠️ ${strategy.name} failed: ${error}`);

        // If not the last strategy, continue to next
        if (strategy !== strategies[strategies.length - 1]) {
          logger.info('🔄 Trying next strategy...');
          continue;
        }

        // Last strategy failed, throw error
        throw new Error(`All form creation strategies failed. Last error: ${error}`);
      }
    }

    return null;
  }

  /**
   * Click the appropriate template card with enhanced reliability
   */
  private async clickTemplateCardResilient(name: string): Promise<void> {
    logger.info(`🎯 Enhanced template clicking for: ${name}`);

    const clickStrategies = [
      {
        name: 'Primary Selector Strategy',
        execute: async () => {
          if (name === '空白のフォーム') {
            const selectors = [
              '.step-create-form',
              'div:has-text("空白のフォーム")',
              '[data-testid*="blank"]',
              '.template-card:has-text("空白のフォーム")'
            ];

            for (const selector of selectors) {
              try {
                const element = this.page.locator(selector).first();
                if (await element.isVisible({ timeout: 3000 })) {
                  await element.click();
                  logger.info(`✅ Clicked using selector: ${selector}`);
                  return;
                }
              } catch (error) {
                continue;
              }
            }
            throw new Error('No blank form selectors worked');
          } else {
            // For other templates, use text-based selection with multiple strategies
            const selectors = [
              `text=${name}`,
              `div:has-text("${name}")`,
              `button:has-text("${name}")`,
              `[aria-label*="${name}"]`
            ];

            for (const selector of selectors) {
              try {
                const element = this.page.locator(selector).first();
                if (await element.isVisible({ timeout: 3000 })) {
                  await element.click();
                  logger.info(`✅ Clicked using selector: ${selector}`);
                  return;
                }
              } catch (error) {
                continue;
              }
            }
            throw new Error(`No selectors worked for template: ${name}`);
          }
        }
      },
      {
        name: 'Force Click Strategy',
        execute: async () => {
          logger.info('🔧 Attempting force click strategy');

          if (name === '空白のフォーム') {
            const element = this.page.locator('.step-create-form').first();
            await element.waitFor({ state: 'attached', timeout: 5000 });
            await element.click({ force: true });
          } else {
            const element = this.page.getByText(name).first();
            await element.waitFor({ state: 'attached', timeout: 5000 });
            await element.click({ force: true });
          }

          logger.info('✅ Force click completed');
        }
      },
      {
        name: 'Scroll and Click Strategy',
        execute: async () => {
          logger.info('📜 Attempting scroll and click strategy');

          if (name === '空白のフォーム') {
            const element = this.page.locator('.step-create-form').first();
            await element.scrollIntoViewIfNeeded();
            await this.page.waitForTimeout(1000);
            await element.click();
          } else {
            const element = this.page.getByText(name).first();
            await element.scrollIntoViewIfNeeded();
            await this.page.waitForTimeout(1000);
            await element.click();
          }

          logger.info('✅ Scroll and click completed');
        }
      }
    ];

    // Try click strategies in order
    for (const strategy of clickStrategies) {
      try {
        logger.info(`🚀 Trying: ${strategy.name}`);
        await strategy.execute();
        logger.info(`✅ ${strategy.name} succeeded`);

        // Wait a moment for click to register
        await this.page.waitForTimeout(1000);
        return;
      } catch (error) {
        logger.warn(`⚠️ ${strategy.name} failed: ${error}`);

        if (strategy !== clickStrategies[clickStrategies.length - 1]) {
          continue;
        }

        // All strategies failed
        throw new Error(`All click strategies failed for template "${name}". Last error: ${error}`);
      }
    }
  }

  /**
   * Store form ID for cleanup (enhanced version)
   */
  private async storeFormIdForCleanup(formId: string): Promise<void> {
    try {
      const { TestContext } = await import('../utils/TestContext');
      const testContext = TestContext.getInstance();

      // Store in multiple places for reliability
      const existingFormIds = testContext.getTestData('createdFormIds') || [];
      const updatedFormIds = [...existingFormIds, formId];

      testContext.setTestData('createdFormIds', updatedFormIds);
      testContext.setTestData('currentFormId', formId);

      logger.info(`📝 Stored form ID for cleanup: ${formId} (Total tracked: ${updatedFormIds.length})`);
    } catch (error) {
      logger.warn(`⚠️ Failed to store form ID for cleanup: ${error}`);
    }
  }



  /**
   * Open form row by name
   */
  async openRowByName(name: string): Promise<void> {
    await this.rowByName(name).click();
    logger.info(`✅ Opened form: ${name}`);
  }

  /**
   * Get form row by name
   */
  async getRow(formName: string): Promise<Locator> {
    return this.rowCell(formName);
  }

  // Hardened helper methods using accessible selectors



  /**
   * Get status cell in a row (ステータス column)
   */
  statusCellIn(row: Locator): Locator {
    return row.locator('td').nth(2);
  }

  /**
   * Get updated date cell in a row
   */
  updatedCellIn(row: Locator): Locator {
    return row.locator('td').nth(3);
  }

  /**
   * Get name cell in a row
   */
  nameCellIn(row: Locator): Locator {
    return row.locator('td').nth(1);
  }

  // ---- in-row actions ----
  // Implement helpers that scope to TD[actions] (usually last cell)
  actionsCellIn(row: Locator): Locator {
    return row.locator('td').last();
  }

  shareBtnIn(row: Locator): Locator {
    return this.actionsCellIn(row).getByRole('button', { name: /共有|シェア|Share/ });
  }

  editBtnIn(row: Locator): Locator {
    return this.actionsCellIn(row).getByRole('button', { name: /編集|Edit/ });
  }

  moreButtonIn(row: Locator): Locator {
    // main selector: icon button that opens the row menu
    const btn = row.locator('.step-handle-form[aria-haspopup="true"]').first();

    // fallback (if class changes): any IconButton within the actions cell
    const fallback = row
      .locator('[class*=IconButton][aria-haspopup="true"]')
      .or(row.locator('button[aria-haspopup="true"]'))
      .first();

    return btn.or(fallback);
  }

  async openMoreForRowByName(name: string): Promise<void> {
    const row = this.rowByName(name);

    // Try multiple approaches to find the more button
    let btn = this.moreButtonIn(row);

    // If the primary selector doesn't work, try fallbacks
    const btnCount = await btn.count();
    if (btnCount === 0) {
      console.log('Primary more button selector failed, trying fallbacks...');

      // Fallback 1: Any button with aria-haspopup in the row
      btn = row.locator('button[aria-haspopup="true"]').first();

      if (await btn.count() === 0) {
        // Fallback 2: Last button in the actions cell
        const actionsCell = this.actionsCellIn(row);
        btn = actionsCell.locator('button').last();

        if (await btn.count() === 0) {
          // Debug: dump all buttons in the row
          const allButtons = await row.locator('button').all();
          console.log(`Found ${allButtons.length} buttons in row`);
          for (let i = 0; i < allButtons.length; i++) {
            const btnInfo = await allButtons[i].getAttribute('class');
            const ariaLabel = await allButtons[i].getAttribute('aria-label');
            const ariaHaspopup = await allButtons[i].getAttribute('aria-haspopup');
            console.log(`Button ${i}: class="${btnInfo}", aria-label="${ariaLabel}", aria-haspopup="${ariaHaspopup}"`);
          }
          throw new Error('Could not find more button in row');
        }
      }
    }

    await btn.scrollIntoViewIfNeeded();
    await expect(btn, 'More button should be visible').toBeVisible();
    await expect(btn, 'More button should be enabled').toBeEnabled();

    await btn.click();

    try {
      await this.expectMoreMenuOpen();
    } catch {
      // one gentle retry in case of transient overlay/focus
      await this.page.waitForTimeout(150);
      await btn.click();
      await this.expectMoreMenuOpen();
    }
  }

  private _moreMenu?: Locator;

  getMoreMenu(): Locator {
    // expose for assertions
    if (this._moreMenu) return this._moreMenu;
    // best-effort fallback if called directly
    return this.page.getByRole('menu').or(this.page.locator('[role="menu"]')).last();
  }

  async expectMoreMenuOpen(): Promise<void> {
    // MUI menus: role="menu" in a portal
    const menu = this.page.getByRole('menu').or(this.page.locator('[role="menu"]')).last();
    await expect(menu, 'Row More menu should be visible').toBeVisible();
    this._moreMenu = menu;
  }

  async menuShouldContainItems(expectedLabels: string[]): Promise<void> {
    const menu = this.getMoreMenu();
    for (const label of expectedLabels) {
      const item = menu.getByRole('menuitem', { name: label })
        .or(menu.locator(`[role="menuitem"]:has-text("${label}")`));
      await expect(item, `Menu item not found: ${label}`).toBeVisible();
    }
  }

  // (optional) quick debug helper you can call once to see live labels
  async dumpMoreMenuItems(): Promise<void> {
    const menu = this.getMoreMenu();
    const texts = await menu.locator('[role="menuitem"]').allInnerTexts();
    console.log('More menu items:', texts);
  }

  /**
   * Get report button in a row using accessible name
   */
  reportBtnIn(row: Locator): Locator {
    return row.getByRole('button', { name: 'レポート' });
  }

  // Row finder by id or name
  rowByFormId(formId: string): Locator {
    return this.rows.filter({
      has: this.page.locator(`[data-row-id="${formId}"]`)
    }).first();
  }

  rowByName(name: string): Locator {
    return this.rows.filter({
      has: this.page.locator('td').filter({ hasText: name })
    }).first();
  }

  /**
   * Perform row action (edit, report, share, more) - hardened version
   */
  async rowAction(name: string, action: '編集' | 'レポート' | '共有' | 'その他'): Promise<void> {
    const row = this.rowCell(name);

    // Close any overlays first
    await this.closeOverlays();

    // Try multiple button selector strategies
    const buttonSelectors = [
      row.getByRole('button', { name: action }),
      row.getByRole('button').filter({ hasText: action }),
      row.locator(`button[aria-label*="${action}"]`),
      row.locator(`button[title*="${action}"]`),
      row.locator(`button:has-text("${action}")`),
      // Icon-based selectors for common actions
      action === 'レポート' ? row.getByTestId('BarChartIcon').locator('..') : null,
      action === '編集' ? row.getByTestId('EditIcon').locator('..') : null,
      action === '共有' ? row.getByTestId('ShareIcon').locator('..') : null,
    ].filter(Boolean);

    let btn = null;
    for (const selector of buttonSelectors) {
      try {
        if (await selector!.isVisible({ timeout: 1000 })) {
          btn = selector;
          break;
        }
      } catch (error) {
        // Continue to next selector
      }
    }

    if (!btn) {
      throw new Error(`Could not find ${action} button for form: ${name}`);
    }

    // Make button reliably clickable
    await btn.scrollIntoViewIfNeeded();
    await btn.hover();

    // Try trial click first to check if clickable
    try {
      await btn.click({ trial: true });
    } catch (error) {
      logger.debug('Trial click failed, proceeding with force click');
    }

    await btn.click({ force: true });

    // Wait for any navigation or modal to appear
    await this.page.waitForTimeout(1000);

    logger.info(`✅ Performed ${action} action on form: ${name}`);
  }

  /**
   * Get status of a form
   */
  async getStatus(name: string): Promise<string> {
    return await this.statusChip(name).innerText();
  }

  /**
   * Get updated date of a form
   */
  async getUpdatedAt(name: string): Promise<string> {
    return await this.updatedAtText(name).innerText();
  }

  /**
   * Get responses count of a form
   */
  async getResponses(name: string): Promise<string> {
    return await this.responsesText(name).innerText();
  }

  /**
   * Navigate to specific page
   */
  async paginate(to: 'first' | 'prev' | 'next' | 'last'): Promise<void> {
    const map = {
      first: this.firstPageBtn,
      prev: this.prevPageBtn,
      next: this.nextPageBtn,
      last: this.lastPageBtn
    };

    const button = map[to];
    if (await button.isEnabled()) {
      await button.click();
    }
    logger.info(`✅ Navigated to ${to} page`);
  }

  /**
   * Navigate to next page
   */
  async nextPage(): Promise<void> {
    await this.paginate('next');
  }

  /**
   * Navigate to previous page
   */
  async prevPage(): Promise<void> {
    await this.paginate('prev');
  }

  /**
   * Navigate to first page
   */
  async firstPage(): Promise<void> {
    await this.paginate('first');
  }

  /**
   * Navigate to last page
   */
  async lastPage(): Promise<void> {
    await this.paginate('last');
  }

  /**
   * Expect specific page number
   */
  async expectPageNumber(n: number): Promise<void> {
    const currentPageIndicator = this.page.locator('[aria-current="page"]');
    await expect(currentPageIndicator).toContainText(n.toString());
    logger.info(`✅ Confirmed on page ${n}`);
  }







  /**
   * Set rows per page - improved to avoid strict mode violations
   */
  async setRowsPerPage(count: number): Promise<void> {
    logger.info(`📊 Setting rows per page to: ${count}`);

    // Use more specific selector for the pagination select
    const paginationSelect = this.page.locator('.MuiTablePagination-select').first();
    await paginationSelect.waitFor({ state: 'visible', timeout: 10000 });
    await paginationSelect.scrollIntoViewIfNeeded();

    // Click to open the dropdown
    await paginationSelect.click();

    // Wait for dropdown to open and select the specific option
    const option = this.page.getByRole('option', { name: count.toString(), exact: true });
    await option.waitFor({ state: 'visible', timeout: 5000 });
    await option.click();

    // Wait for URL to update with more flexible patterns
    await this.page.waitForFunction(
      (expectedCount) => {
        const url = window.location.href;
        return url.includes(`perPage=${expectedCount}`) ||
               url.includes(`per_page=${expectedCount}`) ||
               url.includes(`pageSize=${expectedCount}`) ||
               url.includes(`limit=${expectedCount}`);
      },
      count,
      { timeout: 10000 }
    );

    // Wait for network response
    try {
      await this.page.waitForResponse(r => r.url().includes('/forms') && r.request().method() === 'GET', { timeout: 5000 });
    } catch (error) {
      logger.debug('No network response detected, continuing...');
    }

    logger.info(`✅ Set rows per page to: ${count}`);
  }

  /**
   * Get all template cards
   */
  async getTemplateCards(): Promise<Locator[]> {
    return [
      this.template_Blank,
      this.template_Contact,
      this.template_Quick,
      this.template_Application,
      this.template_Reservation
    ];
  }

  /**
   * Get table row count
   */
  async getRowCount(): Promise<number> {
    const rows = this.page.getByRole('row').filter({ hasNot: this.page.getByRole('columnheader') });
    const count = await rows.count();
    logger.info(`📊 Table has ${count} rows`);
    return count;
  }

  /**
   * Get visible row count (alias for getRowCount)
   */
  async visibleRowCount(): Promise<number> {
    return await this.getRowCount();
  }

  /**
   * Close overlays that might block interactions
   */
  async closeOverlays(): Promise<void> {
    try {
      // Close tour/joyride overlays (common cause of "Target not visible")
      const tourOverlays = [
        '.react-joyride__overlay',
        '.joyride-overlay',
        '[data-tour-elem]',
        '[class*="tour"]',
        '[class*="joyride"]',
        '[data-testid*="tour"]',
        '.driver-popover',
        '.MuiBackdrop-root',
        '.shepherd-modal-overlay-container',
      ];

      for (const selector of tourOverlays) {
        const overlay = this.page.locator(selector);
        if (await overlay.isVisible({ timeout: 500 })) {
          await overlay.click();
          logger.info(`🚫 Closed overlay: ${selector}`);
          await this.page.waitForTimeout(300);
        }
      }

      // Close tour buttons (Skip, Next, Close, Got it)
      const tourButtons = [
        this.page.getByRole('button', { name: /skip|close|dismiss|got it|next|finish|done/i }),
        this.page.locator('button[data-action*="skip"]'),
        this.page.locator('button[data-action*="close"]'),
        this.page.locator('.react-joyride__close'),
        this.page.locator('[data-driver-close-btn], .driver-close-btn'),
        this.page.locator('button:has-text("×")'),
        this.page.locator('button:has-text("閉じる")'),
      ];

      for (const button of tourButtons) {
        if (await button.isVisible({ timeout: 500 })) {
          await button.click();
          logger.info('🚫 Closed tour button');
          await this.page.waitForTimeout(300);
        }
      }

      // Close any other modal overlays
      const modalOverlay = this.page.locator('.MuiModal-root, .MuiDialog-root, [role="dialog"]');
      if (await modalOverlay.isVisible({ timeout: 1000 })) {
        const escapeBtn = modalOverlay.locator('[aria-label="close"], button:has-text("×"), button:has-text("閉じる")');
        if (await escapeBtn.isVisible({ timeout: 500 })) {
          await escapeBtn.click();
          logger.info('🚫 Closed modal overlay');
        }
      }

      // Multiple escape key presses to close any remaining overlays
      await this.page.keyboard.press('Escape');
      await this.page.waitForTimeout(200);
      await this.page.keyboard.press('Escape');
      await this.page.waitForTimeout(200);
      await this.page.keyboard.press('Escape');

    } catch (error) {
      logger.debug('No overlays to close');
    }
  }

  /**
   * Expect specific row count
   */
  async expectRowCount(n: number): Promise<void> {
    const actualCount = await this.getRowCount();
    expect(actualCount).toBe(n);
    logger.info(`✅ Confirmed ${n} rows in table`);
  }

  /**
   * Open share modal for row by name
   */
  async openShareForRowByName(name: string): Promise<void> {
    await this.rowAction(name, '共有');
    logger.info(`✅ Opened share modal for: ${name}`);
  }

  /**
   * Open editor for row by name
   */
  async openEditorForRowByName(name: string): Promise<void> {
    await this.rowAction(name, '編集');
    logger.info(`✅ Opened editor for: ${name}`);
  }

  /**
   * Open analytics for row by name
   */
  async openAnalyticsForRowByName(name: string): Promise<void> {
    await this.rowAction(name, 'レポート');
    logger.info(`✅ Opened analytics for: ${name}`);
  }

  /**
   * Expect status for specific row
   */
  async expectStatusForRow(name: string, status: "公開中" | "非公開" | "公開予約"): Promise<void> {
    const actualStatus = await this.getStatus(name);
    expect(actualStatus).toContain(status);
    logger.info(`✅ Confirmed status "${status}" for form: ${name}`);
  }

  /**
   * Get share button locator for row (helper for share.steps.ts)
   */
  getShareButtonLocatorForRow(name: string): Locator {
    const row = this.page.getByRole('row').filter({ hasText: name });
    return row.getByRole('button', { name: /共有|share/i });
  }



  /**
   * Verify page elements are present
   */
  async verifyPageElements(): Promise<void> {
    logger.info('🔍 Verifying page elements');

    await Promise.all([
      this.brand.waitFor({ state: 'visible' }),
      this.newFormSection.waitFor({ state: 'visible' }),
      this.col_Name.waitFor({ state: 'visible' }),
      this.col_Status.waitFor({ state: 'visible' }),
      this.col_UpdatedAt.waitFor({ state: 'visible' }),
      this.col_Responses.waitFor({ state: 'visible' })
    ]);

    // Verify template cards
    const templates = await this.getTemplateCards();
    for (const template of templates) {
      await template.waitFor({ state: 'visible' });
    }

    logger.info('✅ All page elements verified');
  }

  /**
   * Clean up created forms via API
   * This method should be called in After hooks to keep the environment clean
   */
  static async cleanupCreatedForms(): Promise<void> {
    try {
      const { TestContext } = await import('../utils/TestContext');
      const testContext = TestContext.getInstance();
      const createdFormIds = testContext.getTestData('createdFormIds') || [];

      if (createdFormIds.length === 0) {
        logger.info('🧹 No forms to clean up');
        return;
      }

      logger.info(`🧹 Cleaning up ${createdFormIds.length} created forms`);

      // Import FormsClient for API cleanup
      const { FormsClient } = await import('../clients/formsClient');
      const formsClient = new FormsClient();

      for (const formId of createdFormIds) {
        try {
          await formsClient.deleteForm(formId);
          logger.info(`✅ Deleted form: ${formId}`);
        } catch (error) {
          logger.warn(`⚠️ Failed to delete form ${formId}: ${error}`);
        }
      }

      // Clear the stored form IDs
      testContext.setTestData('createdFormIds', []);
      logger.info('🧹 Form cleanup completed');

    } catch (error) {
      logger.error(`❌ Error during form cleanup: ${error}`);
    }
  }
}
