/**
 * Core Utilities Index
 * Centralized exports for all utility modules
 */

// Core Test Framework
export { TestContext } from './TestContext';
export { expectEventually } from './expectEventually';
export { logger } from './logger';

// Authentication & Security
export { 
  ensureAuthenticated, 
  logoutViaUI, 
  isAuthenticated,
  performUILogin,
  saveAuthState,
  loadAuthState
} from './auth';

// Data Management
export { TestDataManager } from './TestDataManager';
export { TestDataParser } from './TestDataParser';

// UI Utilities
export { dismissCoachmarkIfPresent } from './coachmark-utils';
export { CustomMatchers } from './CustomMatchers';

// Reporting & Integration
export { PlaywrightReportIntegration } from './PlaywrightReportIntegration';
export * from './ReportTypes';

// Type Definitions
export type {
  TestResult,
  ReportData,
  TestSummary,
  CIInfo,
  AIAnalysis,
  AIInsight
} from './ReportTypes';

export type {
  UserData,
  TestDataSet
} from './TestDataManager';
