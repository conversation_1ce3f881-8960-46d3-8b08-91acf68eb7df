import { FullConfig } from '@playwright/test';
import { logger } from '../utils/logger';
import { globalTeardownHook } from '../utils/PlaywrightReportIntegration';
import * as fs from 'fs';
import * as path from 'path';

// eslint-disable-next-line no-unused-vars
async function globalTeardown(_config: FullConfig): Promise<void> {
  logger.info('🏁 Starting global teardown...');

  // Archive old logs if they exist
  const logsDir = 'logs';
  const archiveDir = path.join(logsDir, 'archive');
  
  if (fs.existsSync(logsDir)) {
    const logFiles = fs.readdirSync(logsDir).filter(file => file.endsWith('.log'));
    
    if (logFiles.length > 0) {
      if (!fs.existsSync(archiveDir)) {
        fs.mkdirSync(archiveDir, { recursive: true });
      }
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      logFiles.forEach(file => {
        const oldPath = path.join(logsDir, file);
        const newPath = path.join(archiveDir, `${timestamp}-${file}`);
        try {
          fs.copyFileSync(oldPath, newPath);
          logger.info(`📦 Archived log file: ${file}`);
        } catch (error) {
          logger.warn(`⚠️ Failed to archive log file ${file}: ${error}`);
        }
      });
    }
  }

  // Generate summary report
  const resultsFile = 'reports/playwright-results.json';
  if (fs.existsSync(resultsFile)) {
    try {
      const results = JSON.parse(fs.readFileSync(resultsFile, 'utf8'));
      const summary = {
        total: results.suites?.reduce((acc: number, suite: any) => acc + (suite.specs?.length || 0), 0) || 0,
        passed: 0,
        failed: 0,
        skipped: 0,
      };

      // Calculate test results (simplified)
      logger.info('📊 Test Summary:');
      logger.info(`   Total: ${summary.total}`);
      logger.info(`   Passed: ${summary.passed}`);
      logger.info(`   Failed: ${summary.failed}`);
      logger.info(`   Skipped: ${summary.skipped}`);
    } catch (error) {
      logger.warn(`⚠️ Failed to parse test results: ${error}`);
    }
  }

  // Run HTML report integration teardown
  await globalTeardownHook();

  logger.info('✅ Global teardown completed successfully');
}

export default globalTeardown;
