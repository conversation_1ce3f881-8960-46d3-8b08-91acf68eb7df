import { defineConfig, devices } from '@playwright/test';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const environment = process.env.NODE_ENV || 'development';
const isHeadless = process.env.HEADLESS === 'true' || process.env.CI === 'true';

export default defineConfig({
  testDir: './features',
  timeout: 60_000,
  expect: { timeout: 10_000 },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 1 : 0,
  workers: process.env.CI ? 1 : 4,
  reporter: [
    ['html', { open: 'never', outputFolder: 'reports/playwright-report' }],
    ['line'],
    ['json', { outputFile: 'reports/playwright-results.json' }],
    ['junit', { outputFile: 'reports/playwright-junit.xml' }],
    ['allure-playwright', { outputFolder: 'reports/allure-results' }],
  ],
  use: {
    baseURL: process.env.BASE_URL || getBaseURL(environment),
    headless: isHeadless,
    trace: 'retain-on-failure',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,
    acceptDownloads: true,
    actionTimeout: 30000,
    navigationTimeout: 30000,
  },
  projects: [
    { name: 'chromium', use: { ...devices['Desktop Chrome'] } },
    { name: 'firefox',  use: { ...devices['Desktop Firefox'] } },
    { name: 'webkit',   use: { ...devices['Desktop Safari'] } }
  ],
  outputDir: 'test-results/',
  globalSetup: require.resolve('./config/global-setup.ts'),
  globalTeardown: require.resolve('./config/global-teardown.ts'),
});

function getBaseURL(env: string): string {
  switch (env) {
  case 'production':
    return process.env.PROD_BASE_URL || 'https://prod.smoothcontact.com';
  case 'staging':
    return process.env.STAGING_BASE_URL || 'https://smoothcontact-web.bindec-app-stage.web-life.co.jp';
  case 'development':
  default:
    return process.env.DEV_BASE_URL || 'http://localhost:3000';
  }
}
