export default {
  default: {
    require: [
      'step-definitions/**/*.ts',
      'hooks.ts',
      'hooks-multi-browser.ts',
      'ts-node/register'
    ],
    format: [
      'progress', 
      'json:reports/json/cucumber.json',
      'html:reports/html/cucumber.html'
    ],
    publishQuiet: true,
    failFast: false,
    tags: process.env.TAGS || '@forms',
    worldParameters: {
      browser: process.env.BROWSER || 'chromium'
    }
  },
  
  forms: {
    require: [
      'step-definitions/**/*.ts',
      'hooks.ts',
      'hooks-multi-browser.ts',
      'ts-node/register'
    ],
    format: [
      'progress', 
      'json:reports/json/cucumber-forms.json',
      'html:reports/html/cucumber-forms.html'
    ],
    publishQuiet: true,
    failFast: false,
    tags: '@forms',
    worldParameters: {
      browser: process.env.BROWSER || 'chromium'
    }
  },

  'forms-quick': {
    require: [
      'step-definitions/**/*.ts',
      'hooks.ts',
      'hooks-multi-browser.ts',
      'ts-node/register'
    ],
    format: [
      'progress', 
      'json:reports/json/cucumber-forms-quick.json'
    ],
    publishQuiet: true,
    failFast: false,
    tags: '@forms and not @perf',
    worldParameters: {
      browser: process.env.BROWSER || 'chromium'
    }
  }
}
