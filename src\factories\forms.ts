import { FormsClient } from '../clients/formsClient';
import { logger } from '../utils/logger';

export interface FormData {
  id?: string;
  name: string;
  description?: string;
  template?: string;
  status: 'draft' | 'published' | 'scheduled' | 'unpublished' | 'expired';
  fields?: FormField[];
  settings?: FormSettings;
  publishedAt?: string;
  scheduledAt?: string;
  createdAt?: string;
  updatedAt?: string;
  responsesCount?: number;
  shareUrl?: string;
}

export interface FormField {
  id: string;
  type: 'text' | 'email' | 'number' | 'textarea' | 'select' | 'radio' | 'checkbox';
  label: string;
  required: boolean;
  placeholder?: string;
  options?: string[];
}

export interface FormSettings {
  allowMultipleSubmissions: boolean;
  requireLogin: boolean;
  showProgressBar: boolean;
  customCss?: string;
  redirectUrl?: string;
  emailNotifications: boolean;
}

export class FormsFactory {
  private formsClient: FormsClient;
  private static createdForms: string[] = [];

  constructor() {
    this.formsClient = new FormsClient();
  }

  /**
   * Create a basic form
   */
  async createForm(data: Partial<FormData> = {}): Promise<FormData> {
    const defaultForm: FormData = {
      name: `Test Form ${Date.now()}`,
      description: 'Test form created by automation',
      template: 'blank',
      status: 'draft',
      fields: [
        {
          id: 'name',
          type: 'text',
          label: '名前',
          required: true,
          placeholder: 'お名前を入力してください'
        },
        {
          id: 'email',
          type: 'email',
          label: 'メールアドレス',
          required: true,
          placeholder: '<EMAIL>'
        }
      ],
      settings: {
        allowMultipleSubmissions: false,
        requireLogin: false,
        showProgressBar: true,
        emailNotifications: true
      }
    };

    const formData = { ...defaultForm, ...data };
    
    logger.info(`📝 Creating form: ${formData.name}`);

    try {
      const createdForm = await this.formsClient.createForm(formData);

      // Track created form for cleanup
      if (createdForm.id) {
        FormsFactory.createdForms.push(createdForm.id);
      }

      logger.info(`✅ Form created with ID: ${createdForm.id}`);
      return createdForm;
    } catch (error) {
      logger.error(`❌ Failed to create form via API: ${error}`);

      // For now, just re-throw. UI fallback can be implemented later if needed
      if (process.env.USE_UI_SETUP === '1') {
        throw new Error('UI fallback not implemented yet - please configure API access (SC_API_TOKEN)');
      }

      throw error;
    }
  }

  /**
   * Create a contact form
   */
  async createContactForm(name?: string): Promise<FormData> {
    const contactForm: Partial<FormData> = {
      name: name || `Contact Form ${Date.now()}`,
      description: 'お問い合わせフォーム',
      template: 'contact',
      fields: [
        {
          id: 'name',
          type: 'text',
          label: 'お名前',
          required: true,
          placeholder: 'お名前を入力してください'
        },
        {
          id: 'email',
          type: 'email',
          label: 'メールアドレス',
          required: true,
          placeholder: '<EMAIL>'
        },
        {
          id: 'subject',
          type: 'text',
          label: '件名',
          required: true,
          placeholder: 'お問い合わせの件名'
        },
        {
          id: 'message',
          type: 'textarea',
          label: 'メッセージ',
          required: true,
          placeholder: 'お問い合わせ内容をご記入ください'
        }
      ]
    };

    return await this.createForm(contactForm);
  }

  /**
   * Create a survey form
   */
  async createSurveyForm(name?: string): Promise<FormData> {
    const surveyForm: Partial<FormData> = {
      name: name || `Survey Form ${Date.now()}`,
      description: 'クイックアンケート',
      template: 'survey',
      fields: [
        {
          id: 'satisfaction',
          type: 'radio',
          label: '満足度',
          required: true,
          options: ['とても満足', '満足', '普通', '不満', 'とても不満']
        },
        {
          id: 'recommendation',
          type: 'radio',
          label: '推奨度',
          required: true,
          options: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
        },
        {
          id: 'feedback',
          type: 'textarea',
          label: 'ご意見・ご感想',
          required: false,
          placeholder: 'ご意見やご感想をお聞かせください'
        }
      ]
    };

    return await this.createForm(surveyForm);
  }

  /**
   * Publish a form
   */
  async publishForm(formId: string): Promise<FormData> {
    logger.info(`📢 Publishing form: ${formId}`);
    const publishedForm = await this.formsClient.publishForm(formId);
    logger.info(`✅ Form published: ${formId}`);
    return publishedForm;
  }

  /**
   * Unpublish a form
   */
  async unpublishForm(formId: string): Promise<FormData> {
    logger.info(`📝 Unpublishing form: ${formId}`);
    const unpublishedForm = await this.formsClient.unpublishForm(formId);
    logger.info(`✅ Form unpublished: ${formId}`);
    return unpublishedForm;
  }

  /**
   * Schedule form publication
   */
  async schedulePublish(formId: string, publishAt: Date): Promise<FormData> {
    logger.info(`⏰ Scheduling form publication: ${formId} at ${publishAt.toISOString()}`);
    const scheduledForm = await this.formsClient.schedulePublish(formId, publishAt.toISOString());
    logger.info(`✅ Form publication scheduled: ${formId}`);
    return scheduledForm;
  }

  /**
   * Delete a form
   */
  async deleteForm(formId: string): Promise<void> {
    logger.info(`🗑️ Deleting form: ${formId}`);
    await this.formsClient.deleteForm(formId);
    
    // Remove from tracking
    const index = FormsFactory.createdForms.indexOf(formId);
    if (index > -1) {
      FormsFactory.createdForms.splice(index, 1);
    }
    
    logger.info(`✅ Form deleted: ${formId}`);
  }

  /**
   * Create multiple forms for testing
   */
  async createMultipleForms(count: number, status: FormData['status'] = 'draft'): Promise<FormData[]> {
    logger.info(`📝 Creating ${count} forms with status: ${status}`);
    
    const forms: FormData[] = [];
    
    for (let i = 0; i < count; i++) {
      const form = await this.createForm({
        name: `Test Form ${i + 1} - ${Date.now()}`,
        status: 'draft' // Always create as draft first
      });
      
      // Update status if needed
      if (status === 'published') {
        await this.publishForm(form.id!);
        form.status = 'published';
      } else if (status === 'scheduled') {
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + 1);
        await this.schedulePublish(form.id!, futureDate);
        form.status = 'scheduled';
      }
      
      forms.push(form);
    }
    
    logger.info(`✅ Created ${count} forms`);
    return forms;
  }

  /**
   * Create forms with different statuses for testing filters
   */
  async createFormsForFilterTesting(): Promise<{
    published: FormData;
    unpublished: FormData;
    scheduled: FormData;
  }> {
    logger.info('📝 Creating forms for filter testing');
    
    // Create published form
    const publishedForm = await this.createForm({
      name: `Published Form ${Date.now()}`,
      status: 'draft'
    });
    await this.publishForm(publishedForm.id!);
    publishedForm.status = 'published';
    
    // Create unpublished form
    const unpublishedForm = await this.createForm({
      name: `Unpublished Form ${Date.now()}`,
      status: 'draft'
    });
    
    // Create scheduled form
    const scheduledForm = await this.createForm({
      name: `Scheduled Form ${Date.now()}`,
      status: 'draft'
    });
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1);
    await this.schedulePublish(scheduledForm.id!, futureDate);
    scheduledForm.status = 'scheduled';
    
    logger.info('✅ Created forms for filter testing');
    
    return {
      published: publishedForm,
      unpublished: unpublishedForm,
      scheduled: scheduledForm
    };
  }

  /**
   * Clean up all created forms
   */
  async cleanup(): Promise<void> {
    logger.info(`🧹 Cleaning up ${FormsFactory.createdForms.length} created forms`);

    const deletePromises = FormsFactory.createdForms.map(async (formId: string) => {
      try {
        await this.deleteForm(formId);
      } catch (error) {
        logger.warn(`⚠️ Failed to delete form ${formId}:`, error);
      }
    });
    
    await Promise.all(deletePromises);
    FormsFactory.createdForms = [];
    
    logger.info('✅ Cleanup completed');
  }

  /**
   * Get list of created forms for cleanup
   */
  getCreatedForms(): string[] {
    return [...FormsFactory.createdForms];
  }

  /**
   * Add form ID to cleanup list
   */
  trackForm(formId: string): void {
    if (!FormsFactory.createdForms.includes(formId)) {
      FormsFactory.createdForms.push(formId);
    }
  }

  /**
   * Create multiple forms with different statuses for testing
   */
  static async createFormsWithStatuses(statusCounts: Record<string, number>): Promise<FormData[]> {
    const factory = new FormsFactory();
    const forms: FormData[] = [];

    for (const [status, count] of Object.entries(statusCounts)) {
      for (let i = 0; i < count; i++) {
        const form = await factory.createForm({
          name: `${status} Form ${i + 1}`,
          status: status as FormData['status'],
          updatedAt: new Date(Date.now() - (i * 60000)).toISOString()
        });
        forms.push(form);
      }
    }

    logger.info(`✅ Created ${forms.length} forms with different statuses`);
    return forms;
  }

  /**
   * Create multiple forms for pagination testing
   */
  static async createMultipleForms(count: number): Promise<FormData[]> {
    const factory = new FormsFactory();
    const forms: FormData[] = [];

    for (let i = 0; i < count; i++) {
      const form = await factory.createForm({
        name: `Pagination Test Form ${i + 1}`,
        status: i % 2 === 0 ? 'published' : 'unpublished',
        updatedAt: new Date(Date.now() - (i * 60000)).toISOString()
      });
      forms.push(form);
    }

    logger.info(`✅ Created ${count} forms for pagination testing`);
    return forms;
  }

  /**
   * Create form with responses
   */
  static async createFormWithResponses(responseCount: number = 5): Promise<FormData> {
    const factory = new FormsFactory();
    const form = await factory.createForm({
      name: `Form with ${responseCount} Responses`,
      status: 'published',
      responsesCount: responseCount
    });

    logger.info(`✅ Created form with ${responseCount} responses: ${form.name}`);
    return form;
  }

  /**
   * Create a scheduled form
   */
  static async createScheduledForm(scheduledDate?: Date): Promise<FormData> {
    const factory = new FormsFactory();
    const futureDate = scheduledDate || new Date(Date.now() + 24 * 60 * 60 * 1000);

    return await factory.createForm({
      name: `Scheduled Form ${Date.now()}`,
      status: 'scheduled',
      scheduledAt: futureDate.toISOString()
    });
  }

  /**
   * Static cleanup method for all created forms
   */
  static async cleanup(): Promise<void> {
    const factory = new FormsFactory();

    for (const formId of FormsFactory.createdForms) {
      try {
        await factory.formsClient.deleteForm(formId);
        logger.debug(`🗑️ Deleted form: ${formId}`);
      } catch (error) {
        logger.warn(`⚠️ Failed to delete form ${formId}: ${error}`);
      }
    }

    FormsFactory.createdForms = [];
    logger.info(`🧹 Cleanup completed`);
  }

  /**
   * Get list of created form IDs
   */
  static getCreatedFormIds(): string[] {
    return [...FormsFactory.createdForms];
  }
}
