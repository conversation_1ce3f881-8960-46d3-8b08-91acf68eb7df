{"name": "smoothcontact-automation", "version": "1.0.0", "description": "Comprehensive Playwright + TypeScript automation testing framework with Cucumber and Allure reporting", "main": "index.js", "scripts": {"test": "cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --format @cucumber/pretty-formatter --format json:reports/cucumber-report.json", "test:headless": "cross-env HEADLESS=true npm run test", "test:tag": "cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --tags", "test:parallel": "cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --parallel 3", "test:dev": "cross-env NODE_ENV=development npm run test", "test:staging": "cross-env NODE_ENV=staging npm run test", "test:prod": "cross-env NODE_ENV=production npm run test", "test:login": "cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --tags '@login' features/login/*.feature", "test:login:multi": "npx ts-node scripts/run-multi-browser-login.ts", "test:forms": "cucumber-js -p forms", "test:forms:all": "cross-env TAGS='@forms' npm run test:forms:chromium && npm run test:forms:firefox && npm run test:forms:webkit", "test:forms:chromium": "cross-env BROWSER='chromium' TAGS='@forms' cucumber-js -p forms", "test:forms:firefox": "cross-env BROWSER='firefox' TAGS='@forms' cucumber-js -p forms", "test:forms:webkit": "cross-env BROWSER='webkit' TAGS='@forms' cucumber-js -p forms", "test:forms:ci": "cross-env HEADLESS=true TAGS='@forms' npm run test:forms:all", "test:forms:quick": "cucumber-js -p forms-quick", "test:forms:smoke": "cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --tags \"@smoke and @forms\" 'features/forms/*.feature'", "test:forms:regression": "cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --tags \"@regression and @forms\" 'features/forms/*.feature'", "test:forms:visual": "cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --tags \"@visual and @forms\" 'features/forms/*.feature'", "test:forms:a11y": "cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --tags \"@a11y and @forms\" 'features/forms/*.feature'", "test:forms:perf": "cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --tags \"@perf and @forms\" 'features/forms/*.feature'", "report": "allure generate reports/allure-results --clean -o reports/allure-report && allure open reports/allure-report", "report:serve": "allure serve reports/allure-results", "report:allure": "allure generate reports/allure-results --clean -o reports/allure-report && allure open reports/allure-report", "report:html": "playwright show-report", "report:premium": "npx ts-node tools/scripts/generate-report.ts --type html --template premium --open", "report:nextgen": "npx ts-node tools/scripts/generate-report.ts --type nextgen --template nextgen --open", "report:ai": "npx ts-node tools/scripts/generate-report.ts --type html --template ai-enhanced --ai --open", "report:json": "npx ts-node tools/scripts/generate-report.ts --type json", "report:clean": "npx ts-node tools/scripts/generate-report.ts --clean", "report:generate": "npx ts-node tools/scripts/generate-report.ts --type html --template premium --open", "report:playwright": "npx playwright show-report reports/playwright-report", "test:full": "npm run test:forms:chromium && npm run test:login && npm run report:nextgen", "clean": "rimraf reports/allure-results reports/allure-report reports/cucumber-report.json logs/*.log", "lint": "eslint . --ext .ts,.js --fix", "format": "prettier --write \"**/*.{ts,js,json,md}\"", "type-check": "tsc --noEmit", "setup": "playwright install", "validate": "node scripts/validate-setup.js", "postinstall": "npm run setup"}, "keywords": ["playwright", "typescript", "cucumber", "automation", "testing", "e2e", "allure", "page-object-model"], "author": "SmoothContact Team", "license": "MIT", "devDependencies": {"@cucumber/cucumber": "^10.0.1", "@cucumber/pretty-formatter": "^1.0.1", "@playwright/test": "^1.40.0", "@types/node": "^20.8.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "allure-commandline": "^2.24.1", "allure-cucumberjs": "^2.5.0", "allure-playwright": "^3.3.3", "axe-playwright": "^2.1.0", "cross-env": "^7.0.3", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.3", "rimraf": "^5.0.5", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "dependencies": {"@google/generative-ai": "^0.24.1", "dotenv": "^16.6.1", "tsconfig-paths": "^4.2.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "zod": "^4.0.17"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}