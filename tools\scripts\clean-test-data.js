const fs = require('fs');
const path = require('path');

/**
 * Clean Test Data Script
 * Removes duplicate tests and ensures proper unique test IDs
 */

function cleanTestData() {
  console.log('🧹 Starting test data cleanup...');
  
  try {
    // Read the combined report
    const combinedReportPath = 'reports/combined-report.json';
    if (!fs.existsSync(combinedReportPath)) {
      console.error('❌ Combined report not found. Please run tests first.');
      return;
    }
    
    const combinedData = JSON.parse(fs.readFileSync(combinedReportPath, 'utf8'));
    console.log(`📊 Processing ${combinedData.length} features...`);
    
    let totalTests = 0;
    let duplicatesRemoved = 0;
    let testsWithoutIds = 0;
    
    const cleanedData = combinedData.map(feature => {
      if (!feature.elements) return feature;
      
      const seenIds = new Set();
      const cleanedElements = [];
      
      feature.elements.forEach(element => {
        totalTests++;
        
        // Check if element has proper ID and name
        if (!element.id || !element.name) {
          testsWithoutIds++;
          console.log(`⚠️  Skipping test without proper ID: ${element.name || 'Unknown'}`);
          return;
        }
        
        // Check for duplicates
        if (seenIds.has(element.id)) {
          duplicatesRemoved++;
          console.log(`🗑️  Removing duplicate: ${element.name} (ID: ${element.id})`);
          return;
        }
        
        // Check for generic names that indicate duplicates
        if (element.name === 'Password validation messages' && !element.id.includes('unique')) {
          // Only keep the first one and give it a unique ID
          if (!seenIds.has('password-validation-unique')) {
            element.id = 'login-functionality;password-validation-unique';
            element.name = 'LGI-05: Password validation messages';
            seenIds.add('password-validation-unique');
            cleanedElements.push(element);
          } else {
            duplicatesRemoved++;
            console.log(`🗑️  Removing duplicate password validation test`);
          }
          return;
        }
        
        seenIds.add(element.id);
        cleanedElements.push(element);
      });
      
      return {
        ...feature,
        elements: cleanedElements
      };
    });
    
    // Write cleaned data back
    fs.writeFileSync(combinedReportPath, JSON.stringify(cleanedData, null, 2));
    
    // Create backup of original login report and clean it
    const loginReportPath = 'reports/login-report.json';
    if (fs.existsSync(loginReportPath)) {
      // Backup original
      fs.copyFileSync(loginReportPath, 'reports/login-report-backup.json');
      
      // Clean login report
      const loginData = JSON.parse(fs.readFileSync(loginReportPath, 'utf8'));
      const cleanedLoginData = cleanLoginReport(loginData);
      fs.writeFileSync(loginReportPath, JSON.stringify(cleanedLoginData, null, 2));
      
      console.log('✅ Login report cleaned and backed up');
    }
    
    console.log('');
    console.log('📊 Cleanup Summary:');
    console.log(`   📋 Total tests processed: ${totalTests}`);
    console.log(`   🗑️  Duplicates removed: ${duplicatesRemoved}`);
    console.log(`   ⚠️  Tests without IDs: ${testsWithoutIds}`);
    console.log(`   ✅ Clean tests remaining: ${totalTests - duplicatesRemoved - testsWithoutIds}`);
    console.log('');
    console.log('✨ Test data cleanup complete!');
    
    return {
      totalTests,
      duplicatesRemoved,
      testsWithoutIds,
      cleanTests: totalTests - duplicatesRemoved - testsWithoutIds
    };
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
    throw error;
  }
}

function cleanLoginReport(loginData) {
  console.log('🧹 Cleaning login report...');
  
  return loginData.map(feature => {
    if (!feature.elements) return feature;
    
    const seenIds = new Set();
    const cleanedElements = [];
    
    feature.elements.forEach((element, index) => {
      // Skip tests without proper structure
      if (!element.id || !element.name) {
        console.log(`⚠️  Skipping malformed test: ${element.name || 'Unknown'}`);
        return;
      }
      
      // Handle password validation duplicates
      if (element.name === 'Password validation messages') {
        if (!seenIds.has('password-validation')) {
          // Keep only the first one with a proper ID
          element.id = 'login-functionality;lgi-05-password-validation-messages';
          element.name = 'LGI-05: Password validation messages';
          seenIds.add('password-validation');
          cleanedElements.push(element);
          console.log('✅ Kept first password validation test with unique ID');
        } else {
          console.log('🗑️  Removed duplicate password validation test');
        }
        return;
      }
      
      // Check for other duplicates
      if (seenIds.has(element.id)) {
        console.log(`🗑️  Removing duplicate: ${element.name}`);
        return;
      }
      
      seenIds.add(element.id);
      cleanedElements.push(element);
    });
    
    return {
      ...feature,
      elements: cleanedElements
    };
  });
}

function validateCleanedData() {
  console.log('🔍 Validating cleaned data...');
  
  try {
    const combinedData = JSON.parse(fs.readFileSync('reports/combined-report.json', 'utf8'));
    
    let totalTests = 0;
    let validTests = 0;
    const allIds = new Set();
    const duplicateIds = new Set();
    
    combinedData.forEach(feature => {
      if (!feature.elements) return;
      
      feature.elements.forEach(element => {
        totalTests++;
        
        if (element.id && element.name) {
          validTests++;
          
          if (allIds.has(element.id)) {
            duplicateIds.add(element.id);
          } else {
            allIds.add(element.id);
          }
        }
      });
    });
    
    console.log('📊 Validation Results:');
    console.log(`   📋 Total tests: ${totalTests}`);
    console.log(`   ✅ Valid tests: ${validTests}`);
    console.log(`   🔄 Unique IDs: ${allIds.size}`);
    console.log(`   ❌ Duplicate IDs: ${duplicateIds.size}`);
    
    if (duplicateIds.size > 0) {
      console.log('⚠️  Remaining duplicates:');
      duplicateIds.forEach(id => console.log(`     - ${id}`));
    } else {
      console.log('✨ No duplicates found - data is clean!');
    }
    
    return {
      totalTests,
      validTests,
      uniqueIds: allIds.size,
      duplicateIds: duplicateIds.size
    };
    
  } catch (error) {
    console.error('❌ Validation error:', error.message);
    return null;
  }
}

// Main execution
if (require.main === module) {
  console.log('🚀 Starting Test Data Cleanup Process...');
  console.log('============================================');
  
  try {
    const cleanupResults = cleanTestData();
    console.log('');
    
    const validationResults = validateCleanedData();
    console.log('');
    
    if (validationResults && validationResults.duplicateIds === 0) {
      console.log('🎉 SUCCESS: Test data is now clean and ready!');
      console.log('');
      console.log('📋 Next Steps:');
      console.log('   1. Regenerate reports: npm run report:generate');
      console.log('   2. Verify results in browser');
      console.log('   3. Commit clean data to Git');
    } else {
      console.log('⚠️  WARNING: Some issues remain. Please review the validation results.');
    }
    
  } catch (error) {
    console.error('❌ Cleanup process failed:', error.message);
    process.exit(1);
  }
}

module.exports = {
  cleanTestData,
  validateCleanedData,
  cleanLoginReport
};
