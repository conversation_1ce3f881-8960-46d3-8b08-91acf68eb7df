import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';
import { logger } from '../utils/logger';

export class FormBuilderEditPage extends BasePage {
  override readonly page: Page;

  // App bar / nav
  readonly dashboardLink: Locator;
  readonly topAvatarButton: Locator;

  // Mode tabs (left icon rail)
  readonly tabEditor: Locator;
  readonly tabDesign: Locator;
  readonly tabSubmit: Locator;        // 回答の送信
  readonly tabPublishSettings: Locator;// 公開設定
  readonly tabOtherSettings: Locator;  // その他設定
  readonly tabIntegrations: Locator;   // アプリ埋め込みと外部連携
  readonly tabHelp: Locator;           // ヘルプ

  // Toolbar actions (top-right)
  readonly btnPreview: Locator;
  readonly btnShare: Locator;          // 共有
  readonly btnSave: Locator;           // 保存
  readonly btnResetEdits: Locator;     // 編集をリセット
  readonly btnPublishMenu: Locator;    // フォームを公開

  // Breadcrumb / title
  readonly breadcrumbDashboard: Locator; // ダッシュボード
  readonly breadcrumbEditor: Locator;    // エディター
  readonly formTitle: Locator;           // 空白のフォーム
  readonly formVisibilityChip: Locator;  // 非公開

  // Left palette sections
  readonly sectionAddElements: Locator;  // 要素を追加
  // Palette items (draggables) – robust via data-rbd-draggable-id
  readonly dragText: Locator;            // テキスト
  readonly dragParagraph: Locator;       // 段落テキスト
  readonly dragDescription: Locator;     // 説明文
  readonly dragDateTime: Locator;        // 日時
  readonly dragFullName: Locator;        // 氏名
  readonly dragPhone: Locator;           // 電話番号
  readonly dragEmail: Locator;           // メールアドレス
  readonly dragAddress: Locator;         // 住所
  readonly dragBirthday: Locator;        // 生年月日
  readonly dragSelect: Locator;          // プルダウン
  readonly dragRadio: Locator;           // ラジオボタン
  readonly dragChecklist: Locator;       // チェックボックス
  readonly dragFileUpload: Locator;      // 添付ファイル

  // Canvas (droppable)
  readonly formCanvas: Locator;          // droppable area
  readonly canvasAddIcon: Locator;       // (+) add inside canvas
  readonly btnConfirm: Locator;          // 確認

  // Right inspector (empty state)
  readonly inspectorEmptyTitle: Locator; // 項目を選択してください
  readonly inspectorEmptyHint: Locator;  // 選択すると、ここに詳細設定が表示されます

  constructor(page: Page) {
    super(page, '/form-builder/editor');
    this.page = page;

    // App bar / nav
    this.dashboardLink = page.getByRole('link', { name: 'ダッシュボード' });
    // Avatar has no aria-label; scope within header and pick the only icon button with an Avatar
    this.topAvatarButton = page.locator('header').getByRole('button').first();

    // Mode tabs (use aria-labels on icon buttons)
    this.tabEditor            = page.getByRole('button', { name: 'エディター' });
    this.tabDesign            = page.getByRole('button', { name: 'デザイン' });
    this.tabSubmit            = page.getByRole('button', { name: '回答の送信' });
    this.tabPublishSettings   = page.getByRole('button', { name: '公開設定' });
    this.tabOtherSettings     = page.getByRole('button', { name: 'その他設定' });
    this.tabIntegrations      = page.getByRole('button', { name: 'アプリ埋め込みと外部連携' });
    this.tabHelp              = page.getByRole('button', { name: 'ヘルプ' });

    // Toolbar actions
    this.btnPreview       = page.getByTitle('Preview'); // title attribute
    this.btnShare         = page.getByRole('button', { name: '共有' });
    this.btnSave          = page.getByRole('button', { name: '保存' });
    this.btnResetEdits    = page.getByRole('button', { name: '編集をリセット' });
    this.btnPublishMenu   = page.getByRole('button', { name: /フォームを公開/ });

    // Breadcrumb / title cluster
    this.breadcrumbDashboard = page.getByRole('link', { name: 'ダッシュボード' });
    this.breadcrumbEditor    = page.getByText('エディター', { exact: true });
    this.formTitle           = page.getByText('空白のフォーム', { exact: true });
    this.formVisibilityChip  = page.getByText('非公開', { exact: true });

    // Left palette
    this.sectionAddElements  = page.getByRole('heading', { name: '要素を追加' });
    // Precise draggables by id
    this.dragText        = page.locator('[data-rbd-draggable-id="text-field"]');
    this.dragParagraph   = page.locator('[data-rbd-draggable-id="multiline-text-field"]');
    this.dragDescription = page.locator('[data-rbd-draggable-id="comment"]');
    this.dragDateTime    = page.locator('[data-rbd-draggable-id="date-field"]');
    this.dragFullName    = page.locator('[data-rbd-draggable-id="fullname"]');
    this.dragPhone       = page.locator('[data-rbd-draggable-id="phone"]');
    this.dragEmail       = page.locator('[data-rbd-draggable-id="email"]');
    this.dragAddress     = page.locator('[data-rbd-draggable-id="address"]');
    this.dragBirthday    = page.locator('[data-rbd-draggable-id="birthday"]');
    this.dragSelect      = page.locator('[data-rbd-draggable-id="select-drop-down"]');
    this.dragRadio       = page.locator('[data-rbd-draggable-id="radio-group"]');
    this.dragChecklist   = page.locator('[data-rbd-draggable-id="checklist"]');
    this.dragFileUpload  = page.locator('[data-rbd-draggable-id="file-upload"]');

    // Canvas + controls
    this.formCanvas     = page.locator('[data-rbd-droppable-id="form_droppable"]');
    // Avoid generic testid collisions: scope the add icon within canvas
    this.canvasAddIcon  = this.formCanvas.getByTestId('AddIcon').first();
    this.btnConfirm     = page.getByRole('button', { name: '確認' });

    // Inspector (right panel)
    this.inspectorEmptyTitle = page.getByRole('heading', { name: '項目を選択してください' });
    this.inspectorEmptyHint  = page.getByText('選択すると、ここに詳細設定が表示されます', { exact: true });
  }

  /**
   * Check if form builder editor is loaded
   */
  async isLoaded(): Promise<boolean> {
    try {
      await Promise.all([
        this.btnSave.waitFor({ state: 'visible', timeout: 10000 }),
        this.formCanvas.waitFor({ state: 'visible', timeout: 10000 }),
        this.sectionAddElements.waitFor({ state: 'visible', timeout: 10000 })
      ]);
      logger.info('✅ Form Builder editor loaded successfully');
      return true;
    } catch (error) {
      logger.error('❌ Form Builder editor failed to load', error);
      return false;
    }
  }

  /**
   * Wait for the app header and left palette to be visible
   */
  async waitForReady(): Promise<void> {
    logger.info('⏳ Waiting for Form Builder editor to be ready');

    try {
      // Check current URL first
      const currentUrl = this.page.url();
      logger.info(`📍 Current URL: ${currentUrl}`);

      if (!currentUrl.includes('/form-builder/edit/')) {
        throw new Error(`Not on Form Builder editor page. Current URL: ${currentUrl}`);
      }

      await Promise.all([
        this.dashboardLink.waitFor({ state: 'visible', timeout: 15000 }),
        this.sectionAddElements.waitFor({ state: 'visible', timeout: 15000 }),
        this.formCanvas.waitFor({ state: 'visible', timeout: 15000 })
      ]);

      // Wait for network to settle
      await this.page.waitForLoadState('networkidle');
      logger.info('✅ Form Builder editor is ready');
    } catch (error) {
      logger.error('❌ Form Builder editor failed to load');
      logger.error(`📍 Current URL: ${this.page.url()}`);
      logger.error(`📄 Page title: ${await this.page.title()}`);

      // Take a screenshot for debugging
      try {
        await this.page.screenshot({ fullPage: true });
        logger.error('📸 Screenshot taken for debugging');
      } catch (screenshotError) {
        logger.error('❌ Failed to take screenshot:', screenshotError);
      }

      throw error;
    }
  }

  // -------- Helper actions --------

  /**
   * Open publish menu
   */
  async openPublishMenu(): Promise<void> {
    logger.info('📤 Opening publish menu');
    await this.btnPublishMenu.click();
    logger.info('✅ Publish menu opened');
  }

  /**
   * Open preview
   */
  async openPreview(): Promise<void> {
    logger.info('👁️ Opening preview');
    await this.btnPreview.click();
    logger.info('✅ Preview opened');
  }

  /**
   * Assert save button is disabled
   */
  async assertSaveDisabled(): Promise<void> {
    await expect(this.btnSave).toBeDisabled();
    logger.info('✅ Save button is disabled as expected');
  }

  /**
   * Assert save button is enabled
   */
  async assertSaveEnabled(): Promise<void> {
    await expect(this.btnSave).toBeEnabled();
    logger.info('✅ Save button is enabled as expected');
  }

  /**
   * Wait for save button to become enabled (with timeout)
   */
  async waitForSaveButtonEnabled(timeout = 10000): Promise<void> {
    logger.info('⏳ Waiting for save button to become enabled');

    // Since manual testing shows it takes 1-2 seconds, wait with appropriate timeout
    await expect(this.btnSave).toBeEnabled({ timeout });
    logger.info('✅ Save button became enabled');
  }

  /**
   * Wait for save button to become disabled (with timeout)
   */
  async waitForSaveButtonDisabled(timeout = 10000): Promise<void> {
    logger.info('⏳ Waiting for save button to become disabled');
    await expect(this.btnSave).toBeDisabled({ timeout });
    logger.info('✅ Save button became disabled');
  }

  /**
   * Click save button
   */
  async save(): Promise<void> {
    logger.info('💾 Saving form');

    // Ensure save button is enabled before clicking
    await expect(this.btnSave).toBeEnabled();

    await this.btnSave.click();

    // Wait for save confirmation or network idle
    await this.page.waitForLoadState('networkidle');

    // Wait for save button to become disabled again (indicating save completed)
    await this.waitForSaveButtonDisabled(15000);

    logger.info('✅ Form saved successfully');
  }

  /**
   * Drag text element to canvas
   */
  async dragTextToCanvas(): Promise<void> {
    logger.info('🖱️ Dragging text element to canvas');

    // Use the improved drag logic
    await this.dragPaletteItemToCanvas('テキスト');

    logger.info('✅ Text element dragged to canvas');
  }

  /**
   * Drag any palette item to canvas
   */
  async dropAnyPaletteItemToCanvas(item: Locator): Promise<void> {
    logger.info('🖱️ Dragging palette item to canvas');

    // Use the same realistic drag approach as dragPaletteItemToCanvas
    logger.info('🎯 Performing realistic drag operation with mouse movements');

    // Get the bounding boxes for source and target
    const itemBox = await item.boundingBox();
    const canvasBox = await this.formCanvas.boundingBox();

    if (!itemBox || !canvasBox) {
      logger.warn('⚠️ Could not get bounding boxes, falling back to simple drag');
      await item.dragTo(this.formCanvas);
    } else {
      // Calculate source and target positions
      const sourceX = itemBox.x + itemBox.width / 2;
      const sourceY = itemBox.y + itemBox.height / 2;
      const targetX = canvasBox.x + canvasBox.width / 2;
      const targetY = canvasBox.y + canvasBox.height / 2;

      logger.info(`🎯 Dragging from (${sourceX}, ${sourceY}) to (${targetX}, ${targetY})`);

      // Perform manual drag with mouse events
      await this.page.mouse.move(sourceX, sourceY);
      await this.page.mouse.down();
      await this.page.waitForTimeout(100); // Small delay to simulate human behavior

      // Move to target in steps to simulate realistic drag
      const steps = 5;
      for (let i = 1; i <= steps; i++) {
        const x = sourceX + (targetX - sourceX) * (i / steps);
        const y = sourceY + (targetY - sourceY) * (i / steps);
        await this.page.mouse.move(x, y);
        await this.page.waitForTimeout(50);
      }

      await this.page.mouse.up();
    }

    // Wait for the drag operation to complete and form state to update
    await this.page.waitForTimeout(3000);

    logger.info('✅ Palette item dragged to canvas');
  }



  /**
   * Get the form ID from URL
   */
  async getFormId(): Promise<string> {
    try {
      // Extract from URL
      const url = await this.getCurrentUrl();
      const match = url.match(/\/form-builder\/edit\/([^\/\?]+)/);
      const formId = match ? match[1] : '';
      logger.info(`📋 Form ID from URL: ${formId}`);
      return formId;
    } catch (error) {
      logger.error('❌ Failed to get form ID', error);
      return '';
    }
  }

  /**
   * Navigate back using browser back button
   */
  async backToList(): Promise<void> {
    logger.info('🔙 Going back to forms list');
    await this.page.goBack();
    await this.page.waitForLoadState('networkidle');
    logger.info('✅ Returned to forms list');
  }

  /**
   * Verify editor is loaded with form data
   */
  async verifyEditorLoaded(): Promise<void> {
    logger.info('🔍 Verifying editor is loaded');

    await Promise.all([
      this.verifyElementVisible(this.btnSave),
      this.verifyElementVisible(this.btnPublishMenu),
      this.verifyElementVisible(this.formCanvas)
    ]);

    logger.info('✅ Editor verification complete');
  }

  /**
   * Get palette item by Japanese name
   */
  getPaletteItemByName(itemName: string): Locator {
    const itemMap: { [key: string]: Locator } = {
      'テキスト': this.dragText,
      '段落テキスト': this.dragParagraph,
      '説明文': this.dragDescription,
      '日時': this.dragDateTime,
      '氏名': this.dragFullName,
      '電話番号': this.dragPhone,
      'メールアドレス': this.dragEmail,
      '住所': this.dragAddress,
      '生年月日': this.dragBirthday,
      'プルダウン': this.dragSelect,
      'ラジオボタン': this.dragRadio,
      'チェックボックス': this.dragChecklist,
      '添付ファイル': this.dragFileUpload
    };

    const item = itemMap[itemName];
    if (!item) {
      throw new Error(`Unknown palette item: ${itemName}`);
    }
    return item;
  }

  /**
   * Debug method to list all available palette items
   */
  async debugPaletteItems(): Promise<void> {
    logger.info('🔍 Debugging available palette items...');

    // Find all draggable items in the palette
    const allDraggables = this.page.locator('[data-rbd-draggable-id]');
    const count = await allDraggables.count();

    logger.info(`🔍 Found ${count} draggable items in total`);

    for (let i = 0; i < count; i++) {
      const item = allDraggables.nth(i);
      const id = await item.getAttribute('data-rbd-draggable-id');
      const text = await item.textContent();
      logger.info(`🔍 Item ${i}: ID="${id}", Text="${text}"`);
    }
  }

  /**
   * Drag specific palette item to canvas by name
   */
  async dragPaletteItemToCanvas(itemName: string): Promise<void> {
    logger.info(`🖱️ Dragging ${itemName} to canvas`);

    // Debug available palette items if we can't find the requested one
    try {
      const item = this.getPaletteItemByName(itemName);
      const itemCount = await item.count();
      if (itemCount === 0) {
        logger.warn(`⚠️ Item not found: ${itemName}, debugging available items...`);
        await this.debugPaletteItems();
        throw new Error(`Palette item not found: ${itemName}`);
      }
    } catch (error) {
      logger.warn(`⚠️ Error getting palette item: ${itemName}, debugging available items...`);
      await this.debugPaletteItems();
      throw error;
    }

    const item = this.getPaletteItemByName(itemName);

    // Get initial field count before drag
    const canvasFields = this.formCanvas.locator('[data-rbd-draggable-id^="form_draggable"]');
    const initialFieldCount = await canvasFields.count();
    logger.info(`🔍 Initial field count: ${initialFieldCount}`);

    // Ensure item is scrolled into view before attempting drag
    await item.scrollIntoViewIfNeeded();

    // Try multiple drag approaches for better reliability
    logger.info('🎯 Attempting drag operation with multiple strategies');

    try {
      // Strategy 1: Use Playwright's built-in dragTo method (most reliable)
      logger.info('🎯 Strategy 1: Using Playwright dragTo method');
      await item.dragTo(this.formCanvas, {
        force: true,
        timeout: 10000
      });

      // Wait for the operation to complete
      await this.page.waitForTimeout(2000);

      // Check if the drag was successful by looking for increased field count
      const finalFieldCount = await canvasFields.count();

      if (finalFieldCount > initialFieldCount) {
        logger.info(`✅ Strategy 1 successful: ${finalFieldCount} field(s) on canvas (increased from ${initialFieldCount})`);
        return;
      }

      logger.warn(`⚠️ Strategy 1 failed: field count remained ${finalFieldCount} (expected > ${initialFieldCount}), trying Strategy 2`);
    } catch (error) {
      logger.warn('⚠️ Strategy 1 failed with error:', error);
    }

    try {
      // Strategy 2: Manual mouse movements (fallback)
      logger.info('🎯 Strategy 2: Using manual mouse movements');

      // Ensure item is scrolled into view and get fresh coordinates
      await item.scrollIntoViewIfNeeded();
      await this.page.waitForTimeout(500); // Wait for scroll to complete

      const itemBox = await item.boundingBox();
      const canvasBox = await this.formCanvas.boundingBox();

      if (!itemBox || !canvasBox) {
        throw new Error('Could not get bounding boxes');
      }

      // Calculate source and target positions using fresh coordinates
      const sourceX = itemBox.x + itemBox.width / 2;
      const sourceY = itemBox.y + itemBox.height / 2;
      const targetX = canvasBox.x + canvasBox.width / 2;
      const targetY = canvasBox.y + canvasBox.height / 2;

      logger.info(`🎯 Dragging from (${sourceX}, ${sourceY}) to (${targetX}, ${targetY})`);
      logger.info(`🎯 Item box: ${JSON.stringify(itemBox)}, Canvas box: ${JSON.stringify(canvasBox)}`);

      // Hover over the item first to ensure it's interactive
      await this.page.mouse.move(sourceX, sourceY);
      await this.page.waitForTimeout(100);

      // Perform manual drag with mouse events
      await this.page.mouse.down();
      await this.page.waitForTimeout(200); // Longer delay for drag start

      // Move to target in steps with longer delays
      const steps = 8; // More steps for smoother drag
      for (let i = 1; i <= steps; i++) {
        const x = sourceX + (targetX - sourceX) * (i / steps);
        const y = sourceY + (targetY - sourceY) * (i / steps);
        await this.page.mouse.move(x, y);
        await this.page.waitForTimeout(100); // Longer delay between steps
      }

      await this.page.mouse.up();
      await this.page.waitForTimeout(3000); // Longer wait for drop to complete

      // Check if the drag was successful by looking for increased field count
      const finalFieldCount = await canvasFields.count();

      if (finalFieldCount > initialFieldCount) {
        logger.info(`✅ Strategy 2 successful: ${finalFieldCount} field(s) on canvas (increased from ${initialFieldCount})`);
        return;
      }

      logger.warn(`⚠️ Strategy 2 failed: field count remained ${finalFieldCount} (expected > ${initialFieldCount})`);
    } catch (error) {
      logger.warn('⚠️ Strategy 2 failed with error:', error);
    }

    try {
      // Strategy 3: Force drag with hover and click approach
      logger.info('🎯 Strategy 3: Using hover and force drag approach');

      // Ensure item is visible and in viewport
      await item.scrollIntoViewIfNeeded();
      await this.page.waitForTimeout(500);

      // Hover over the item to activate any hover states
      await item.hover();
      await this.page.waitForTimeout(200);

      // Try using Playwright's dragTo with force and different options
      await item.dragTo(this.formCanvas, {
        force: true,
        timeout: 15000,
        sourcePosition: { x: 62, y: 17 }, // Center of typical palette item
        targetPosition: { x: 300, y: 200 } // Center-ish of canvas
      });

      await this.page.waitForTimeout(3000);

      // Check if the drag was successful by looking for increased field count
      const finalFieldCount = await canvasFields.count();

      if (finalFieldCount > initialFieldCount) {
        logger.info(`✅ Strategy 3 successful: ${finalFieldCount} field(s) on canvas (increased from ${initialFieldCount})`);
        return;
      }

      logger.warn(`⚠️ Strategy 3 failed: field count remained ${finalFieldCount} (expected > ${initialFieldCount})`);
    } catch (error) {
      logger.warn('⚠️ Strategy 3 failed with error:', error);
    }

    // If all strategies failed, throw an error
    throw new Error(`Failed to drag ${itemName} to canvas using all available strategies`);
  }



  /**
   * Click mode tab by name
   */
  async clickModeTab(tabName: string): Promise<void> {
    logger.info(`🖱️ Clicking ${tabName} tab`);

    const tabMap: { [key: string]: Locator } = {
      'エディター': this.tabEditor,
      'デザイン': this.tabDesign,
      '回答の送信': this.tabSubmit,
      '公開設定': this.tabPublishSettings,
      'その他設定': this.tabOtherSettings,
      'アプリ埋め込みと外部連携': this.tabIntegrations,
      'ヘルプ': this.tabHelp
    };

    const tab = tabMap[tabName];
    if (!tab) {
      throw new Error(`Unknown tab: ${tabName}`);
    }

    await tab.click();
    logger.info(`✅ ${tabName} tab clicked`);
  }

  /**
   * Verify tab is active
   */
  async verifyTabActive(tabName: string): Promise<void> {
    logger.info(`🔍 Verifying ${tabName} tab is active`);
    const tab = this.getTabByName(tabName);

    // Check for active state - this might need adjustment based on actual implementation
    await expect(tab).toHaveAttribute('aria-selected', 'true');
    logger.info(`✅ ${tabName} tab is active`);
  }

  /**
   * Get tab locator by name
   */
  private getTabByName(tabName: string): Locator {
    const tabMap: { [key: string]: Locator } = {
      'エディター': this.tabEditor,
      'デザイン': this.tabDesign,
      '回答の送信': this.tabSubmit,
      '公開設定': this.tabPublishSettings,
      'その他設定': this.tabOtherSettings,
      'アプリ埋め込みと外部連携': this.tabIntegrations,
      'ヘルプ': this.tabHelp
    };

    const tab = tabMap[tabName];
    if (!tab) {
      throw new Error(`Unknown tab: ${tabName}`);
    }
    return tab;
  }
}
