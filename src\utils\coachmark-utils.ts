import { Page } from '@playwright/test';
import { logger } from './logger';

/**
 * Dismisses Joyride/tour overlays and ensures table is in view
 * This utility addresses the "Target not visible" warnings by removing tour blockers
 */
export async function dismissCoachmarkIfPresent(page: Page): Promise<void> {
  logger.info('🚫 Dismissing coachmarks and ensuring table visibility');

  try {
    // 1. Try to close tour/joyride with close button
    const closeButton = page.locator('[data-test-id="button-close"]');
    if (await closeButton.isVisible({ timeout: 1000 })) {
      await closeButton.click();
      logger.info('✅ Closed tour with close button');
    }

    // 2. Try clicking "次へ" (Next) button up to 3 times to advance through tour
    for (let i = 0; i < 3; i++) {
      const nextButton = page.getByText('次へ');
      if (await nextButton.isVisible({ timeout: 1000 })) {
        await nextButton.click();
        logger.info(`✅ Clicked tour next button (${i + 1}/3)`);
        await page.waitForTimeout(500); // Brief pause between clicks
      } else {
        break;
      }
    }

    // 3. Collapse "新しいフォームを作成" strip if it's expanded (pushing content down)
    const chevronButton = page.locator('.MuiIconButton-sizeSmall');
    if (await chevronButton.isVisible({ timeout: 1000 })) {
      // Check if the collapse section is expanded by looking for visible template cards
      const templateCards = page.locator('.MuiGrid-container .MuiGrid-item');
      const isExpanded = await templateCards.first().isVisible({ timeout: 500 });
      
      if (isExpanded) {
        await chevronButton.click();
        logger.info('✅ Collapsed form creation strip');
        await page.waitForTimeout(300); // Wait for collapse animation
      }
    }

    // 4. Ensure table is scrolled into view
    const table = page.locator('table.MuiTable-root');
    if (await table.isVisible({ timeout: 2000 })) {
      await table.scrollIntoViewIfNeeded();
      logger.info('✅ Scrolled table into view');
    }

    // 5. Wait a moment for any animations to complete
    await page.waitForTimeout(500);

  } catch (error) {
    logger.warn(`⚠️ Error dismissing coachmarks: ${error}`);
    // Don't throw - this is a best-effort cleanup
  }

  logger.info('🚫 Coachmark dismissal completed');
}
