import { Before, After, BeforeAll, AfterAll, Status, setDefaultTimeout } from '@cucumber/cucumber';
import { chromium, <PERSON>rowser, BrowserContext, Page } from '@playwright/test';
import { TestContext } from '../../src/utils/TestContext';
import { ensureAuthenticated } from '../../src/utils/auth';
import { logger } from '../../src/utils/logger';
import { FormsFactory } from '../../src/factories/forms';
import * as dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Set default timeout for all steps to 30 seconds
setDefaultTimeout(30000);

// Load environment variables
dotenv.config();

let browser: Browser;
let context: BrowserContext;
let page: Page;

BeforeAll(async () => {
  logger.info('🚀 Starting test suite...');
  
  // Launch browser
  browser = await chromium.launch({
    headless: process.env.HEADLESS === 'true',
    slowMo: parseInt(process.env.SLOW_MO || '0'),
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });
  
  logger.info('🌐 Browser launched successfully');
});

Before(async (scenario) => {
  logger.scenario(`Starting scenario: ${scenario.pickle.name}`);

  // Create new browser context for each scenario with storageState if available
  const proj = (process.env.BROWSER || process.env.BROWSER_NAME || 'chromium').toLowerCase();
  const statePath = path.resolve('.auth', `${proj}.json`);

  const contextOptions: any = {
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,
    acceptDownloads: true,
    baseURL: process.env.BASE_URL || process.env.STAGING_BASE_URL || 'https://smoothcontact-web.bindec-app-stage.web-life.co.jp',
  };

  // Only load storageState if it exists and this is not a login test
  const isLoginTest = scenario.pickle.tags.some(tag =>
    tag.name === '@login' ||
    tag.name === '@LGI' ||
    scenario.pickle.name.toLowerCase().includes('login') ||
    scenario.pickle.uri?.includes('login')
  );

  if (fs.existsSync(statePath) && !isLoginTest) {
    contextOptions.storageState = statePath;
    logger.info(`💾 Loading storage state from ${statePath}`);
  } else if (isLoginTest) {
    logger.info(`🔓 Skipping storage state for login test: ${scenario.pickle.name}`);
  }

  context = await browser.newContext(contextOptions);

  // Create new page
  page = await context.newPage();
  
  // Set up TestContext
  const testContext = TestContext.getInstance();
  testContext.setBrowser(browser);
  testContext.setContext(context);
  testContext.setPage(page);
  
  // Add console logging
  page.on('console', (msg) => {
    if (msg.type() === 'error') {
      logger.error(`Browser console error: ${msg.text()}`);
    } else if (msg.type() === 'warning') {
      logger.warn(`Browser console warning: ${msg.text()}`);
    }
  });
  
  // Add page error handling
  page.on('pageerror', (error) => {
    logger.error(`Page error: ${error.message}`);
  });
  
  // Add request failure logging
  page.on('requestfailed', (request) => {
    logger.warn(`Request failed: ${request.url()} - ${request.failure()?.errorText}`);
  });

  // Ensure authentication for scenarios that require it (except login tests)
  if (!isLoginTest) {
    try {
      const browserName = process.env.BROWSER_NAME || 'chromium';
      await ensureAuthenticated(context, browserName);
      logger.info(`🔐 Authentication ensured for scenario: ${scenario.pickle.name}`);
    } catch (error) {
      logger.warn(`⚠️ Authentication failed for scenario: ${scenario.pickle.name}, continuing without auth: ${error}`);
    }
  } else {
    logger.info(`🔓 Skipping authentication for login test: ${scenario.pickle.name}`);
  }

  // Store scenario info in TestContext for data seeding
  testContext.setScenarioInfo({
    name: scenario.pickle.name,
    tags: scenario.pickle.tags.map(tag => tag.name)
  });

  logger.info('📄 New page context created for scenario');
});

After(async function (scenario) {
  const testContext = TestContext.getInstance();
  
  if (scenario.result?.status === Status.FAILED) {
    logger.error(`❌ Scenario failed: ${scenario.pickle.name}`);
    
    // Take screenshot on failure
    try {
      const screenshotPath = await testContext.takeScreenshot(`failed-${scenario.pickle.name.replace(/\s+/g, '-')}`);
      logger.info(`📸 Failure screenshot saved: ${screenshotPath}`);
      
      // Attach screenshot to Cucumber report (if using Allure or similar)
      if (this.attach) {
        const screenshot = await page.screenshot({ fullPage: true });
        this.attach(screenshot, 'image/png');
      }
    } catch (error) {
      logger.error(`Failed to take screenshot: ${error}`);
    }
    
    // Save page HTML for debugging
    try {
      const html = await page.content();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fs = require('fs');
      fs.writeFileSync(`logs/failed-page-${timestamp}.html`, html);
      logger.info('💾 Page HTML saved for debugging');
    } catch (error) {
      logger.error(`Failed to save page HTML: ${error}`);
    }
  } else {
    logger.info(`✅ Scenario passed: ${scenario.pickle.name}`);
  }
  
  // Clean up test data (forms, responses, etc.)
  try {
    await FormsFactory.cleanup();
  } catch (error) {
    logger.warn(`⚠️ Cleanup error: ${error}`);
  }

  // Close page and context
  try {
    await page.close();
    await context.close();
    logger.info('🧹 Page and context closed');
  } catch (error) {
    logger.error(`Error closing page/context: ${error}`);
  }

  // Clear test context data
  testContext.clearTestData();
});

AfterAll(async () => {
  logger.info('🏁 Test suite completed');
  
  // Close browser
  if (browser) {
    await browser.close();
    logger.info('🌐 Browser closed');
  }
  
  // Reset test context
  TestContext.reset();
  
  logger.info('✅ Cleanup completed successfully');
});

// Export for use in step definitions
export { browser, context, page };
