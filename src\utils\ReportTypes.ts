// Enhanced Report Data Interfaces for Premium HTML Reports
// Supports modern UI/UX, AI insights, and comprehensive analytics

export interface TestStep {
  name: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: string;
  screenshot?: string;
  startTime?: string;
  endTime?: string;
}

export interface TestHook {
  name: string;
  type: 'before' | 'after' | 'beforeEach' | 'afterEach';
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: string;
  tags?: string[];
}

export interface TestArtifacts {
  screenshots: string[];
  videos: string[];
  traces: string[];
  logs?: string[];
  attachments?: Array<{
    name: string;
    path: string;
    type: string;
    size?: number;
  }>;
}

export interface TestResult {
  id: string;
  name: string;
  status: 'passed' | 'failed' | 'skipped' | 'flaky';
  duration: number;
  tags: string[];
  feature: string;
  scenario?: string;
  browser: string;
  browserVersion?: string;
  os?: string;
  viewport?: string;
  steps: TestStep[];
  hooks?: TestHook[];
  error?: string;
  stackTrace?: string;
  artifacts: TestArtifacts;
  retries: number;
  startTime: string;
  endTime: string;
  flakyHistory?: Array<{
    runId: string;
    status: 'passed' | 'failed';
    date: string;
  }>;
  performance?: {
    memoryUsage?: number;
    cpuUsage?: number;
    networkRequests?: number;
    pageLoadTime?: number;
  };
}

export interface TestSummary {
  total: number;
  passed: number;
  failed: number;
  skipped: number;
  flaky: number;
  retries: number;
  passRate: number;
  avgDuration: number;
  byBrowser: Record<string, {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
    flaky: number;
  }>;
  byFeature: Record<string, {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
    flaky: number;
  }>;
  byTag: Record<string, {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
    flaky: number;
  }>;
}

export interface HistoryEntry {
  runId: string;
  date: string;
  summary: {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
    flaky: number;
    duration: number;
  };
  environment: string;
  branch?: string;
  commit?: string;
}

export interface CIInfo {
  buildNumber?: string;
  buildUrl?: string;
  branch?: string;
  commit?: string;
  commitMessage?: string;
  author?: string;
  pullRequest?: string;
  jobName?: string;
  provider?: 'github' | 'gitlab' | 'jenkins' | 'azure' | 'other';
}

export interface AIInsight {
  type: 'failure_analysis' | 'pattern_detection' | 'recommendation' | 'summary';
  title: string;
  description: string;
  confidence: number; // 0-1
  category: 'critical' | 'warning' | 'info' | 'suggestion';
  relatedTests?: string[]; // Test IDs
  actionItems?: string[];
  generatedAt: string;
}

export interface AIAnalysis {
  enabled: boolean;
  globalInsights: AIInsight[];
  testInsights: Record<string, AIInsight[]>; // Test ID -> insights
  summary: {
    totalInsights: number;
    criticalIssues: number;
    patterns: number;
    recommendations: number;
  };
  processingTime?: number;
  model?: string;
  version?: string;
}

export interface ReportData {
  projectName?: string;
  environment?: string;
  runDate?: string;
  runStart?: string;
  runEnd?: string;
  totalDuration?: number;
  summary: TestSummary;
  tests: TestResult[];
  history?: HistoryEntry[];
  ci?: CIInfo;
  ai?: AIAnalysis;
  analytics?: {
    testStability: any;
    performanceMetrics: any;
    failurePatterns: any;
    trends: any;
    recommendations: string[];
  };
  aiInsights?: {
    enabled: boolean;
    insights: any[];
    summary: any;
  };
  interactive?: {
    filters: any;
    groupings: any;
    charts: any;
  };
  metadata?: {
    reportVersion?: string;
    generatedBy?: string;
    generatedAt?: string;
    framework?: string;
    version?: string;
    environment?: string;
    nodeVersion?: string;
    playwrightVersion?: string;
    customConfig?: Record<string, any>;
  };
}

export interface ReportConfig {
  projectName: string;
  reportsDir: string;
  htmlOutputDir: string;
  maxReports: number;
  openAfterGeneration: boolean;
  cleanup: boolean;
  includeArtifacts: boolean;
  theme: 'light' | 'dark' | 'auto';
  customCSS?: string;
  customJS?: string;
  logo?: string;
  ai: {
    enabled: boolean;
    apiKey?: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
  };
  notifications: {
    slack?: {
      webhook: string;
      channel: string;
      includeAI?: boolean;
    };
    email?: {
      recipients: string[];
      smtpConfig: any;
      includeAI?: boolean;
    };
  };
  charts: {
    enabled: boolean;
    types: Array<'pie' | 'trend' | 'duration' | 'performance'>;
    colors?: Record<string, string>;
  };
  features: {
    darkMode: boolean;
    search: boolean;
    filters: boolean;
    grouping: boolean;
    export: boolean;
    keyboardShortcuts: boolean;
    accessibility: boolean;
  };
}

// Filter and search interfaces
export interface FilterOptions {
  browsers: Set<string>;
  statuses: Set<string>;
  features: Set<string>;
  tags: Set<string>;
  searchQuery: string;
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface GroupingOptions {
  groupBy: 'none' | 'feature' | 'browser' | 'status' | 'tag';
  sortBy: 'name' | 'duration' | 'status' | 'date';
  sortOrder: 'asc' | 'desc';
}

// Chart data interfaces
export interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }>;
}

export interface ChartOptions {
  responsive: boolean;
  maintainAspectRatio: boolean;
  plugins?: {
    legend?: {
      display: boolean;
      position?: 'top' | 'bottom' | 'left' | 'right';
    };
    tooltip?: {
      enabled: boolean;
      callbacks?: Record<string, Function>;
    };
  };
  scales?: Record<string, any>;
}
