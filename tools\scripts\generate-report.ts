#!/usr/bin/env npx ts-node

/**
 * Unified Report Generation CLI
 * Single entry point for all report generation
 */

import { ReportManager, ReportConfig } from '../../reports/ReportManager';
import { logger } from '../../src/utils/logger';

interface CLIOptions {
  type?: string;
  template?: string;
  output?: string;
  open?: boolean;
  ai?: boolean;
  theme?: string;
  clean?: boolean;
  help?: boolean;
}

class ReportGeneratorCLI {
  private reportManager: ReportManager;

  constructor() {
    this.reportManager = new ReportManager();
  }

  public async run(args: string[]): Promise<void> {
    const options = this.parseArgs(args);

    if (options.help) {
      this.showHelp();
      return;
    }

    if (options.clean) {
      this.reportManager.cleanOldReports();
      return;
    }

    const config: ReportConfig = {
      type: (options.type as any) || 'html',
      template: (options.template as any) || 'premium',
      features: [],
      outputPath: options.output,
      openAfterGeneration: options.open || false,
      includeAI: options.ai || false,
      theme: (options.theme as any) || 'auto'
    };

    // Validate configuration
    if (!this.validateConfig(config)) {
      return;
    }

    logger.info('🚀 Starting unified report generation...');
    logger.info(`📊 Type: ${config.type}, Template: ${config.template}`);

    const result = await this.reportManager.generateReport(config);

    if (result.success) {
      logger.info('✅ Report generation completed successfully!');
      if (result.outputPath) {
        logger.info(`📄 Report saved to: ${result.outputPath}`);
      }
      if (result.metadata) {
        logger.info(`📊 Generated at: ${result.metadata.generatedAt}`);
        logger.info(`📦 File size: ${(result.metadata.fileSize / 1024).toFixed(2)} KB`);
      }
    } else {
      logger.error('❌ Report generation failed!');
      if (result.error) {
        logger.error(`Error: ${result.error}`);
      }
      process.exit(1);
    }
  }

  private parseArgs(args: string[]): CLIOptions {
    const options: CLIOptions = {};

    for (let i = 0; i < args.length; i++) {
      const arg = args[i];
      const nextArg = args[i + 1];

      switch (arg) {
        case '--type':
        case '-t':
          options.type = nextArg;
          i++;
          break;
        case '--template':
        case '-T':
          options.template = nextArg;
          i++;
          break;
        case '--output':
        case '-o':
          options.output = nextArg;
          i++;
          break;
        case '--open':
        case '-O':
          options.open = true;
          break;
        case '--ai':
        case '-a':
          options.ai = true;
          break;
        case '--theme':
          options.theme = nextArg;
          i++;
          break;
        case '--clean':
        case '-c':
          options.clean = true;
          break;
        case '--help':
        case '-h':
          options.help = true;
          break;
      }
    }

    return options;
  }

  private validateConfig(config: ReportConfig): boolean {
    const availableTypes = this.reportManager.getAvailableTypes();
    const availableTemplates = this.reportManager.getAvailableTemplates();

    if (!availableTypes.includes(config.type)) {
      logger.error(`❌ Invalid report type: ${config.type}`);
      logger.info(`Available types: ${availableTypes.join(', ')}`);
      return false;
    }

    if (!availableTemplates.includes(config.template)) {
      logger.error(`❌ Invalid template: ${config.template}`);
      logger.info(`Available templates: ${availableTemplates.join(', ')}`);
      return false;
    }

    return true;
  }

  private showHelp(): void {
    console.log(`
🎨 Unified Report Generator

USAGE:
  npx ts-node tools/scripts/generate-report.ts [OPTIONS]

OPTIONS:
  -t, --type <type>        Report type (html, nextgen, json)
  -T, --template <template> Template (basic, premium, nextgen, ai-enhanced)
  -o, --output <path>      Output file path
  -O, --open              Open report after generation
  -a, --ai                Include AI analysis
  --theme <theme>         Theme (light, dark, auto)
  -c, --clean             Clean old reports
  -h, --help              Show this help

EXAMPLES:
  # Generate premium HTML report
  npx ts-node tools/scripts/generate-report.ts --type html --template premium --open

  # Generate NextGen report with AI
  npx ts-node tools/scripts/generate-report.ts --type nextgen --ai --open

  # Generate JSON report
  npx ts-node tools/scripts/generate-report.ts --type json

  # Clean old reports
  npx ts-node tools/scripts/generate-report.ts --clean

AVAILABLE TYPES:
  html     - Premium HTML report with interactive features
  nextgen  - Modern NextGen report with advanced analytics
  json     - Consolidated JSON report

AVAILABLE TEMPLATES:
  basic       - Simple, clean template
  premium     - Professional template with charts
  nextgen     - Modern glass morphism design
  ai-enhanced - AI-powered insights and recommendations
`);
  }
}

// Run CLI if called directly
if (require.main === module) {
  const cli = new ReportGeneratorCLI();
  cli.run(process.argv.slice(2)).catch(error => {
    logger.error('CLI Error:', error);
    process.exit(1);
  });
}

export { ReportGeneratorCLI };
