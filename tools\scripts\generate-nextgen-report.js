#!/usr/bin/env node

/**
 * Next-Generation Test Report Generator Script
 * Automatically generates comprehensive test reports after test execution
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  reportsDir: 'reports',
  jsonReports: [
    'reports/json/cucumber-forms.json',
    'reports/login-report.json'
  ],
  combinedReport: 'reports/combined-report.json',
  outputs: {
    nextgen: 'reports/nextgen-report.html',
    interactive: 'reports/nextgen-interactive-report.html',
    ultimate: 'reports/nextgen-ultimate-report.html',
    aiEnhanced: 'reports/nextgen-ai-enhanced-report.html',
    optimization: 'reports/test-optimization-report.html',
    predictive: 'reports/predictive-analytics-dashboard.html',
    geminiAI: 'reports/gemini-ai-report.html',
    premium: 'reports/premium-report.html',
    ai: 'reports/ai-powered-report.html'
  }
};

async function generateNextGenReport() {
  try {
    console.log('🚀 Starting Next-Generation Report Generation...');
    console.log('=' .repeat(60));
    
    // Step 1: Verify test reports exist
    console.log('📋 Step 1: Verifying test reports...');
    const existingReports = CONFIG.jsonReports.filter(report => fs.existsSync(report));
    
    if (existingReports.length === 0) {
      console.log('⚠️  No test reports found. Running tests first...');
      await runTests();
    } else {
      console.log(`✅ Found ${existingReports.length} test reports`);
      existingReports.forEach(report => console.log(`   📄 ${report}`));
    }
    
    // Step 2: Combine reports
    console.log('\n📊 Step 2: Combining test reports...');
    await combineReports();
    
    // Step 3: Generate Next-Gen report
    console.log('\n🎨 Step 3: Generating Next-Generation report...');
    await generateReport();
    
    // Step 4: Generate additional reports
    console.log('\n💎 Step 4: Generating additional reports...');
    await generateAdditionalReports();
    
    // Step 5: Summary
    console.log('\n🎉 Report Generation Complete!');
    console.log('=' .repeat(60));
    displaySummary();
    
  } catch (error) {
    console.error('❌ Error generating reports:', error.message);
    process.exit(1);
  }
}

async function runTests() {
  console.log('🧪 Running test suites...');
  
  try {
    // Run forms tests
    console.log('   📝 Running forms tests...');
    execSync('npm run test:forms:chromium', { 
      stdio: 'inherit',
      env: {
        ...process.env,
        GEMINI_API_KEY: process.env.GEMINI_API_KEY || "AIzaSyBHJfQZ8_Ky9QVxGxJxKxJxKxJxKxJxKxJ",
        BASE_URL: process.env.BASE_URL || "https://smoothcontact-web.bindec-app-stage.web-life.co.jp",
        VALID_EMAIL: process.env.VALID_EMAIL || "<EMAIL>",
        VALID_PASSWORD: process.env.VALID_PASSWORD || "vietnam5963",
        BROWSER: "chromium",
        HEADLESS: "true"
      }
    });
    
    // Run login tests
    console.log('   🔐 Running login tests...');
    execSync('npx cucumber-js --require-module ts-node/register --require "./step-definitions/**/*.ts" --tags "@login" features/login/login.feature --format json:reports/login-report.json', {
      stdio: 'inherit',
      env: {
        ...process.env,
        BASE_URL: process.env.BASE_URL || "https://smoothcontact-web.bindec-app-stage.web-life.co.jp",
        VALID_EMAIL: process.env.VALID_EMAIL || "<EMAIL>",
        VALID_PASSWORD: process.env.VALID_PASSWORD || "vietnam5963",
        BROWSER: "chromium"
      }
    });
    
    console.log('✅ All tests completed successfully');
    
  } catch (error) {
    console.log('⚠️  Some tests may have failed, but continuing with report generation...');
  }
}

async function combineReports() {
  const combinedData = [];
  
  for (const reportPath of CONFIG.jsonReports) {
    if (fs.existsSync(reportPath)) {
      console.log(`   📄 Reading ${reportPath}...`);
      const data = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
      
      if (Array.isArray(data)) {
        combinedData.push(...data);
      } else {
        combinedData.push(data);
      }
    }
  }
  
  // Ensure reports directory exists
  if (!fs.existsSync(CONFIG.reportsDir)) {
    fs.mkdirSync(CONFIG.reportsDir, { recursive: true });
  }
  
  fs.writeFileSync(CONFIG.combinedReport, JSON.stringify(combinedData, null, 2));
  console.log(`✅ Combined report saved: ${CONFIG.combinedReport}`);
  console.log(`   📊 Total features: ${combinedData.length}`);
}

async function generateReport() {
  try {
    // Generate all Next-Gen report variants
    console.log('   🎨 Generating Next-Generation reports...');

    // Phase 1: Core Next-Gen Reports
    console.log('   📊 Phase 1: Core Reports...');

    // 1. Standard Next-Gen report
    execSync('node generate-fixed-nextgen.js', { stdio: 'inherit' });
    if (fs.existsSync('reports/nextgen-fixed-report.html')) {
      fs.copyFileSync('reports/nextgen-fixed-report.html', CONFIG.outputs.nextgen);
      console.log(`✅ Standard Next-Gen report: ${CONFIG.outputs.nextgen}`);
    }

    // 2. Interactive Next-Gen report
    execSync('node generate-interactive-nextgen.js', { stdio: 'inherit' });
    if (fs.existsSync('reports/nextgen-interactive-report.html')) {
      console.log(`✅ Interactive Next-Gen report: ${CONFIG.outputs.interactive}`);
    }

    // 3. Ultimate Next-Gen report
    execSync('node generate-ultimate-nextgen.js', { stdio: 'inherit' });
    if (fs.existsSync('reports/nextgen-ultimate-report.html')) {
      console.log(`✅ Ultimate Next-Gen report: ${CONFIG.outputs.ultimate}`);
    }

    // Phase 3: AI-Powered Analytics
    console.log('   🧠 Phase 3: AI-Powered Analytics...');

    // 4. AI-Enhanced report
    execSync('node generate-ai-enhanced-report.js', { stdio: 'inherit' });
    if (fs.existsSync('reports/nextgen-ai-enhanced-report.html')) {
      console.log(`✅ AI-Enhanced report: ${CONFIG.outputs.aiEnhanced}`);
    }

    // 5. Test Optimization report
    execSync('node generate-optimization-report.js', { stdio: 'inherit' });
    if (fs.existsSync('reports/test-optimization-report.html')) {
      console.log(`✅ Optimization report: ${CONFIG.outputs.optimization}`);
    }

    // 6. Predictive Analytics dashboard
    execSync('node generate-predictive-dashboard.js', { stdio: 'inherit' });
    if (fs.existsSync('reports/predictive-analytics-dashboard.html')) {
      console.log(`✅ Predictive Analytics: ${CONFIG.outputs.predictive}`);
    }

    // 7. Gemini AI report
    execSync('node generate-gemini-ai-report.js', { stdio: 'inherit' });
    if (fs.existsSync('reports/gemini-ai-report.html')) {
      console.log(`✅ Gemini AI Report: ${CONFIG.outputs.geminiAI}`);
    }

  } catch (error) {
    console.error('❌ Error generating Next-Gen reports:', error.message);

    // Fallback to TypeScript version
    try {
      console.log('   🔧 Trying TypeScript version as fallback...');
      if (!fs.existsSync('dist/NextGenReportGenerator.js')) {
        console.log('   🔧 Compiling TypeScript...');
        execSync('npx tsc utils/NextGenReportGenerator.ts --target es2020 --module commonjs --outDir dist --skipLibCheck', {
          stdio: 'inherit'
        });
      }

      const { NextGenReportGenerator } = require('../dist/NextGenReportGenerator.js');
      const generator = new NextGenReportGenerator();

      await generator.generateFromJsonFile(CONFIG.combinedReport, CONFIG.outputs.nextgen);
      console.log(`✅ Next-Gen report generated (fallback): ${CONFIG.outputs.nextgen}`);

    } catch (fallbackError) {
      console.error('❌ Fallback also failed:', fallbackError.message);
      throw error;
    }
  }
}

async function generateAdditionalReports() {
  // Generate AI-powered report
  console.log('   🤖 Generating AI-powered report...');
  execSync(`node generate-ai-report.js`, { stdio: 'inherit' });
  
  // Copy to standard location
  if (fs.existsSync('reports/ai-powered-report.html')) {
    fs.copyFileSync('reports/ai-powered-report.html', CONFIG.outputs.ai);
    console.log(`✅ AI-powered report: ${CONFIG.outputs.ai}`);
  }
}

function displaySummary() {
  console.log('📊 Generated Reports:');
  console.log('');
  
  const reports = [
    {
      name: '🚀 Next-Generation Report',
      path: CONFIG.outputs.nextgen,
      description: 'Modern UI with glass morphism design and comprehensive analytics'
    },
    {
      name: '⚡ Interactive Next-Gen Report',
      path: CONFIG.outputs.interactive,
      description: 'Advanced filtering, real-time search, and interactive charts'
    },
    {
      name: '🌟 Ultimate Next-Gen Report',
      path: CONFIG.outputs.ultimate,
      description: 'AI insights, collaboration tools, command palette, and advanced analytics'
    },
    {
      name: '🧠 AI-Enhanced Report',
      path: CONFIG.outputs.aiEnhanced,
      description: 'Machine learning insights with predictive analytics and intelligent recommendations'
    },
    {
      name: '🚀 Test Optimization Report',
      path: CONFIG.outputs.optimization,
      description: 'Intelligent test suite optimization with parallel execution planning'
    },
    {
      name: '🔮 Predictive Analytics Dashboard',
      path: CONFIG.outputs.predictive,
      description: 'AI-powered failure forecasting and quality trend analysis'
    },
    {
      name: '🤖 Gemini AI Report',
      path: CONFIG.outputs.geminiAI,
      description: 'Google Gemini AI integration with intelligent chat and analysis'
    },
    {
      name: '🤖 AI-Powered Report',
      path: CONFIG.outputs.ai,
      description: 'Beautiful design with AI-generated insights and recommendations'
    }
  ];
  
  reports.forEach(report => {
    if (fs.existsSync(report.path)) {
      const stats = fs.statSync(report.path);
      const size = (stats.size / 1024).toFixed(1);
      
      console.log(`   ${report.name}`);
      console.log(`   📄 ${report.path} (${size} KB)`);
      console.log(`   📝 ${report.description}`);
      console.log(`   🌐 file:///${path.resolve(report.path)}`);
      console.log('');
    }
  });
  
  console.log('🎯 Quick Actions:');
  console.log('   • Open Standard Report: npm run report:open');
  console.log('   • Open Interactive Report: start reports/nextgen-interactive-report.html');
  console.log('   • Open Ultimate Report: start reports/nextgen-ultimate-report.html');
  console.log('   • Open AI-Enhanced Report: start reports/nextgen-ai-enhanced-report.html');
  console.log('   • Open Optimization Report: start reports/test-optimization-report.html');
  console.log('   • Open Predictive Dashboard: start reports/predictive-analytics-dashboard.html');
  console.log('   • Open Gemini AI Report: start reports/gemini-ai-report.html');
  console.log('   • Generate new reports: npm run report:generate');
  console.log('   • Run tests + reports: npm run test:full');
  console.log('');
  console.log('🧠 Phase 3 Complete: AI-Powered Insights & Automation Delivered! 🚀');
  console.log('🔮 Advanced predictive analytics, intelligent optimization, and automated insights now available!');
  console.log('✨ Happy Testing with AI! 🎉');
}

// Handle command line execution
if (require.main === module) {
  generateNextGenReport().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = {
  generateNextGenReport,
  CONFIG
};
