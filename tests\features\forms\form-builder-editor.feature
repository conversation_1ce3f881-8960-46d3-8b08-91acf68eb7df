@ui @editor @regression
Feature: Form Builder - Editor surface is usable end-to-end
  As a form author
  I want to build, configure, and publish a form
  So that respondents can submit it successfully

  Background:
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    And I verify I see the breadcrumb "ダッシュボード" and "エディター"
    And I verify I see the form title "空白のフォーム" and the visibility chip "非公開"

  @smoke @load
  Scenario: FB-01 Page loads with all key UI clusters visible
    Then I should see the left mode tabs: "エディター", "デザイン", "回答の送信", "公開設定", "その他設定", "アプリ埋め込みと外部連携", "ヘルプ"
    And I should see the top toolbar buttons: "共有", "保存", "編集をリセット"
    And I should see the preview button with title "Preview"
    And I should see the publish control labelled "フォームを公開"
    And I should see the palette section header "要素を追加"
    And I should see the droppable form canvas
    And I should see the inspector empty state: title "項目を選択してください" and hint "選択すると、ここに詳細設定が表示されます"
    # FB-01 Enhancements
    And the publish button should have aria-controls "publish-menu" and that element should exist
    And the initial disabled states for "保存", "共有", "編集をリセット" should match DOM truthiness

  @tabs
  Scenario Outline: FB-02 Switching mode tabs updates the active state without navigation breakage
    When I click the "<tab>" mode tab
    Then the "<tab>" tab should be active
    And the page URL should remain on the same form and editor area
    And I should still see the app header and form title
    # FB-02 Enhancements
    And the "<tab>" tab should have aria-selected "true"
    And the URL hash/query should remain stable after tab click
    And focus should stay on the tab after click

    Examples:
      | tab                     |
      | エディター              |
      | デザイン                |
      | 回答の送信              |
      | 公開設定                |
      | その他設定              |
      | アプリ埋め込みと外部連携|
      | ヘルプ                  |

  @ui @editor @regression @tabs @keyboard @accessibility @enhanced
  Scenario: FB-02 Enhanced: Keyboard navigation for tabs
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I navigate tabs using keyboard "Right"
    Then the tab focus should move to "デザイン"
    When I navigate tabs using keyboard "Right"
    Then the tab focus should move to "回答の送信"
    When I navigate tabs using keyboard "Home"
    Then the tab focus should move to "エディター"
    When I navigate tabs using keyboard "End"
    Then the tab focus should move to "ヘルプ"

  @toolbar @disabled
  Scenario: FB-03 Save button is disabled before any change, enabled after edits, disabled again after save
    Then the "保存" button should be disabled
    When I drag the "テキスト" element from the palette to the form canvas
    Then the "保存" button should be enabled
    When I click the "保存" button
    Then the "保存" button should become disabled

  @toolbar @disabled @enhanced @edge-case
  Scenario: FB-03 Enhanced: Save button re-disables after deleting last field
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    Then the "保存" button should be disabled
    When I drag the "テキスト" element from the palette to the form canvas
    Then the "保存" button should be enabled
    When I delete the last field from the canvas
    Then the save button should re-disable after deleting the last field

  @toolbar @disabled @enhanced @performance @debounce
  Scenario: FB-03 Enhanced: Save button stability during rapid edits
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I make rapid changes to the form
    Then the save button should not flicker or mis-state during rapid edits

  @dragdrop @palette
  Scenario Outline: FB-04 Each palette item can be dragged onto the canvas and becomes selectable
    When I drag the "<paletteItem>" element from the palette to the form canvas
    Then I should see a new "<expectLabel>" field on the canvas
    And the inspector should show settings for "<expectLabel>"

    Examples:
      | paletteItem                  | expectLabel       |
      | テキスト                     | テキスト          |
      | 段落テキスト                 | 段落テキスト      |
      | 説明文                       | 説明文            |
      | 日時                         | 日時              |
      | 氏名                         | 氏名              |
      | 電話番号                     | 電話番号          |
      | メールアドレス               | メールアドレス    |
      | 住所                         | 住所              |
      | 生年月日                     | 生年月日          |
      | プルダウン                   | プルダウン        |
      | ラジオボタン                 | ラジオボタン      |
      | チェックボックス             | チェックボックス  |
      | 添付ファイル                 | 添付ファイル      |

  @dragdrop @reorder
  Scenario: FB-05 Reorder fields within canvas using drag-and-drop
    Given I have added at least two fields to the canvas
    When I reorder the fields by dragging the second field above the first
    Then the order of fields on the canvas should reflect the change
    And no duplicate fields should appear

  @canvas @empty-state
  Scenario: FB-06 Canvas inline add control renders and responds
    Then I should see a (+) add icon inside the canvas
    When I click the (+) add icon inside the canvas
    Then a context menu or inline insertion affordance should appear

  @inspector
  Scenario: FB-07 Inspector shows empty state when no field selected and detail panel when field selected
    Then I should see the inspector empty state
    When I select a field on the canvas
    Then the inspector should display that field's configuration
    When I click on empty canvas area
    Then the inspector should return to empty state

  @toolbar @preview
  Scenario: FB-08 Preview opens in a new view without losing unsaved state
    Given the "保存" button is disabled
    When I click the Preview button with title "Preview"
    Then a preview should open in a new tab or overlay
    And closing the preview should return me to the editor
    And the "保存" button state should remain unchanged

  @toolbar @share
  Scenario: FB-09 Share dialog opens and contains a form link and visibility hints
    When I click the "共有" button
    Then I should see a share dialog
    And the dialog should contain a shareable link or instructions
    And I should see a visibility indicator matching "非公開"

  @toolbar @reset
  Scenario: FB-10 Reset edits reverts unsaved changes and keeps saved content intact
    Given I have made unsaved changes
    When I click "編集をリセット"
    Then I should be prompted to confirm the reset
    When I confirm the reset
    Then my unsaved changes should be discarded
    And the canvas should reflect the last saved state

  @publish @menu
  Scenario: FB-11 Publish menu opens and exposes publish actions
    When I open the publish control labelled "フォームを公開"
    Then I should see publish options suitable for making the form public
    And I should see a "確認" or equivalent confirmation action

  @breadcrumb @nav
  Scenario: FB-12 Breadcrumb links navigate correctly
    When I click the breadcrumb "ダッシュボード"
    Then I should be navigated to the dashboard view
    When I navigate back to the editor
    Then I should again see the editor header and left palette

  @validation @negative
  Scenario: FB-13 Prevent dropping outside canvas and reject invalid drops
    When I attempt to drag a palette item and drop it outside the canvas
    Then no field should be created
    And the "保存" button should remain disabled if there were no other changes

  @a11y @roles
  Scenario: FB-14 Critical controls expose accessible roles and names
    Then the mode tabs should be role "button" with the names listed in FB-02
    And the top actions "共有", "保存", "編集をリセット" should be role "button"
    And the preview control should be discoverable by title "Preview"
    And the canvas should be identifiable as a droppable region

  @persistence
  Scenario: FB-15 Persist content across reload after save
    Given I have added two fields to the canvas
    And I click "保存"
    When I reload the page
    Then both fields should still be present
    And the "保存" button should be disabled

  @selection @editing
  Scenario: FB-16 Selecting a field focuses it and shows handles / action menu
    Given at least one field exists on the canvas
    When I click the field label
    Then the field should enter selected state
    And context actions such as delete/duplicate/move should be available

  @delete
  Scenario: FB-17 Deleting a field removes it from canvas and updates save state
    Given a field exists on the canvas
    When I select the field and choose delete
    Then the field should be removed from the canvas
    And the "保存" button should become disabled
    # Note: Save button becomes disabled because deleting the only field returns to original empty state

  @localization
  Scenario: FB-18 UI strings render in Japanese and remain stable for role/name queries
    Then I should see Japanese labels for tabs, toolbar, palette, and inspector as listed
    And queries by accessible role and name should locate the same elements

  @stability
  Scenario: FB-19 No framework-generated IDs are required for selection
    Then no selector in the tests should rely on ephemeral IDs
    And all elements under test should be located by role/name or stable data attributes

  @multi-add
  Scenario: FB-20 Adding multiple different field types in succession works
    When I add "テキスト", "メールアドレス", and "ラジオボタン" to the canvas
    Then each field type should appear exactly once in the canvas
    And the inspector should update to the last selected field

  @confirm
  Scenario: FB-21 Publish confirmation flow reaches a confirm screen
    When I open the publish control and choose a publish action
    Then I should see a confirmation step including "確認"
    And cancelling should return me to the editor without publishing

  @state
  Scenario: FB-22 Share and Publish dialogs do not toggle Save state by themselves
    Given the "保存" button is disabled
    When I open and close the Share dialog
    And I open and close the Publish menu
    Then the "保存" button should remain disabled

  @undo @redo
  Scenario: FB-23 Undo/redo (if present) revert and reapply last edit
    Given I have added a field
    When I perform Undo
    Then the field should disappear
    When I perform Redo
    Then the field should reappear

  @avatar @menu
  Scenario: FB-24 Avatar menu opens from header
    When I click the avatar button in the header
    Then I should see a user menu

  # Text Field Deep Dive Testing
  @text-field @deep-dive @smoke @add-element
  Scenario: FB-25 Add text field to canvas with full validation
    When I drag the "テキスト" element from the palette to the canvas
    Then I should see a text field added to the canvas
    And the field should display the default label "テキスト"
    And the field should have a text input with placeholder "テキスト"
    And the field should show action buttons: duplicate, drag handle, delete
    And the save button should become enabled

  @text-field @configuration @inspector
  Scenario: FB-26 Text field inspector configuration panel
    Given I have added a text field to the canvas
    When I click on the text field to select it
    Then the inspector should show the text field configuration panel
    And I should see the following configuration sections:
      | Section           | Field Type    | Default Value |
      | 項目タイトル      | text input    | テキスト      |
      | プレースホルダー  | text input    | テキスト      |
      | 項目説明文        | rich text     | empty         |
      | テキスト設定      | radio group   | 文字列        |
      | 回答を必須にする  | checkbox      | unchecked     |
    And I should see the rich text editor for item description
    And I should see input type options: "文字列", "数字", "英数字"

  @text-field @positive-input @label-configuration
  Scenario Outline: FB-27 Configure text field label (positive cases)
    Given I have added a text field to the canvas
    And I have selected the text field
    When I change the "項目タイトル" to "<new_label>"
    Then the field label on the canvas should update to "<new_label>"
    And the inspector should show the updated label "<new_label>"
    And the save button should remain enabled

    Examples:
      | new_label                    |
      | お名前                       |
      | Full Name                    |
      | 氏名（漢字）                 |
      | 123 Numbers                  |

  @text-field @negative-input @label-validation
  Scenario Outline: FB-28 Text field label validation (negative cases)
    Given I have added a text field to the canvas
    And I have selected the text field
    When I try to change the "項目タイトル" to "<invalid_label>"
    Then I should see appropriate validation behavior
    And the field should handle the input gracefully
    And no system errors should occur

    Examples:
      | invalid_label                                                                    |
      | <script>alert('xss')</script>                                                   |
      | ""                                                                              |
      | 🚀🎉💯🔥⭐️🌟✨🎯🎪🎨                                                           |

  @text-field @positive-input @placeholder-configuration
  Scenario Outline: FB-29 Configure text field placeholder (positive cases)
    Given I have added a text field to the canvas
    And I have selected the text field
    When I change the "プレースホルダー" to "<new_placeholder>"
    Then the field input placeholder should update to "<new_placeholder>"
    And the inspector should show the updated placeholder "<new_placeholder>"

    Examples:
      | new_placeholder              |
      | 例：田中太郎                 |
      | Enter your full name         |
      | 半角英数字で入力してください |

  @text-field @input-types @validation
  Scenario Outline: FB-30 Configure text input types
    Given I have added a text field to the canvas
    And I have selected the text field
    When I select the input type "<input_type>"
    Then the field should be configured for "<input_type>" input
    And the field validation should match the selected type
    And the save button should remain enabled

    Examples:
      | input_type |
      | 文字列     |
      | 数字       |
      | 英数字     |

  @text-field @required-field @validation
  Scenario: FB-31 Configure required field validation
    Given I have added a text field to the canvas
    And I have selected the text field
    When I check the "回答を必須にする" checkbox
    Then the field should be marked as required
    And the field should show required indicator on the canvas
    When I uncheck the "回答を必須にする" checkbox
    Then the field should no longer be marked as required

  @text-field @field-actions @duplicate
  Scenario: FB-32 Duplicate text field with configuration
    Given I have added and configured a text field with:
      | Property         | Value           |
      | 項目タイトル     | カスタムラベル  |
      | プレースホルダー | カスタム入力    |
      | 入力タイプ       | 数字            |
      | 必須             | checked         |
    When I click the duplicate button on the text field
    Then a new text field should be added to the canvas
    And the new field should have identical configuration
    And both fields should be independently selectable
    And the save button should remain enabled

  @text-field @field-actions @delete
  Scenario: FB-33 Delete text field with confirmation
    Given I have added a text field to the canvas
    And the field is configured with custom settings
    When I click the delete button on the text field
    Then I should see a confirmation dialog "削除しますか?"
    When I click "項目を削除" to confirm
    Then the text field should be removed from the canvas
    And the canvas should return to empty state
    And the inspector should show empty state
    And the save button should become enabled

  @text-field @persistence @reload
  Scenario: FB-34 Text field persistence after page reload
    Given I have added and configured a text field
    And I have saved the form
    When I reload the page
    Then the text field should still be present
    And all configuration should be preserved
    And the field should be fully functional

  # Paragraph Text (段落テキスト) Deep Dive Testing
  @paragraph-field @deep-dive @smoke @add-element
  Scenario: FB-35 Add paragraph text field to canvas with full validation
    When I drag the "段落テキスト" element from the palette to the canvas
    Then I should see a paragraph field added to the canvas
    And the field should display the default label "段落テキスト"
    And the field should have a textarea with placeholder "段落テキスト"
    And the field should show action buttons: duplicate, drag handle, delete
    And the save button should become enabled

  @paragraph-field @configuration @inspector
  Scenario: FB-36 Paragraph field inspector configuration panel
    Given I have added a paragraph field to the canvas
    When I click on the paragraph field to select it
    Then the inspector should show the paragraph field configuration panel
    And I should see the following paragraph configuration sections:
      | Section           | Field Type    | Default Value |
      | 項目タイトル      | text input    | 段落テキスト  |
      | プレースホルダー  | text input    | 段落テキスト  |
      | 項目説明文        | rich text     | empty         |
      | 回答を必須にする  | checkbox      | unchecked     |
    And I should see the rich text editor for item description

  @paragraph-field @positive-input @multiline-validation
  Scenario Outline: FB-37 Configure paragraph field with multiline content
    Given I have added a paragraph field to the canvas
    And I have selected the paragraph field
    When I change the "項目タイトル" to "<new_label>"
    Then the field label on the canvas should update to "<new_label>"
    And the inspector should show the updated label "<new_label>"
    And the textarea should support multiline input

    Examples:
      | new_label                    |
      | ご意見・ご感想               |
      | Detailed Comments            |
      | 詳細な説明をお聞かせください |

  @paragraph-field @negative-input @validation
  Scenario: FB-38 Paragraph field handles large content gracefully
    Given I have added a paragraph field to the canvas
    And I have selected the paragraph field
    When I try to enter extremely long content in the textarea
    Then the field should handle the large content appropriately
    And no system errors should occur
    And the field should maintain proper formatting

  # Email Address (メールアドレス) Deep Dive Testing
  @email-field @deep-dive @smoke @add-element
  Scenario: FB-39 Add email field to canvas with validation
    When I drag the "メールアドレス" element from the palette to the canvas
    Then I should see an email field added to the canvas
    And the field should display the default label "メールアドレス"
    And the field should have an email input with placeholder "メールアドレス"
    And the field should show action buttons: duplicate, drag handle, delete
    And the save button should become enabled

  @email-field @configuration @inspector
  Scenario: FB-40 Email field inspector configuration panel
    Given I have added an email field to the canvas
    When I click on the email field to select it
    Then the inspector should show the email field configuration panel
    And I should see the following email configuration sections:
      | Section           | Field Type    | Default Value   |
      | 項目タイトル      | text input    | メールアドレス  |
      | プレースホルダー  | text input    | メールアドレス  |
      | 項目説明文        | rich text     | empty           |
      | 回答を必須にする  | checkbox      | unchecked       |
    And the field should have built-in email validation

  @email-field @positive-input @email-validation
  Scenario Outline: FB-41 Email field validates correct email formats
    Given I have added an email field to the canvas
    And I have selected the email field
    When I configure the field with placeholder "<placeholder>"
    Then the field should accept valid email formats
    And the field should show appropriate validation feedback

    Examples:
      | placeholder                    |
      | <EMAIL>             |
      | ユーザー@example.co.jp         |
      | <EMAIL>      |

  @email-field @negative-input @email-validation
  Scenario Outline: FB-42 Email field rejects invalid email formats
    Given I have added an email field to the canvas
    When I test the field with invalid email "<invalid_email>"
    Then the field should show validation error
    And the field should not accept the invalid format
    And appropriate error messaging should be displayed

    Examples:
      | invalid_email     |
      | invalid-email     |
      | @domain.com       |
      | user@             |
      | user space@domain |

  # Phone Number (電話番号) Deep Dive Testing
  @phone-field @deep-dive @smoke @add-element
  Scenario: FB-43 Add phone field to canvas with validation
    When I drag the "電話番号" element from the palette to the canvas
    Then I should see a phone field added to the canvas
    And the field should display the default label "電話番号"
    And the field should have a phone input with placeholder "電話番号"
    And the field should show action buttons: duplicate, drag handle, delete
    And the save button should become enabled

  @phone-field @configuration @inspector
  Scenario: FB-44 Phone field inspector configuration panel
    Given I have added a phone field to the canvas
    When I click on the phone field to select it
    Then the inspector should show the phone field configuration panel
    And I should see the following phone configuration sections:
      | Section           | Field Type    | Default Value |
      | 項目タイトル      | text input    | 電話番号      |
      | プレースホルダー  | text input    | 電話番号      |
      | 項目説明文        | rich text     | empty         |
      | 回答を必須にする  | checkbox      | unchecked     |
    And the field should have built-in phone validation

  @phone-field @positive-input @phone-validation
  Scenario Outline: FB-45 Phone field validates correct phone formats
    Given I have added a phone field to the canvas
    And I have selected the phone field
    When I configure the field with placeholder "<placeholder>"
    Then the field should accept valid phone formats
    And the field should show appropriate validation feedback

    Examples:
      | placeholder           |
      | 03-1234-5678          |
      | 090-1234-5678         |
      | +81-3-1234-5678       |
      | 0120-123-456          |

  @phone-field @negative-input @phone-validation
  Scenario Outline: FB-46 Phone field rejects invalid phone formats
    Given I have added a phone field to the canvas
    When I test the field with invalid phone "<invalid_phone>"
    Then the field should show validation error
    And the field should not accept the invalid format
    And appropriate error messaging should be displayed

    Examples:
      | invalid_phone |
      | 123           |
      | abc-def-ghij  |
      | 12345678901234|
      | +invalid      |

  # Date/Time (日時) Deep Dive Testing
  @date-field @deep-dive @smoke @add-element
  Scenario: FB-47 Add date field to canvas with picker functionality
    When I drag the "日時" element from the palette to the canvas
    Then I should see a date field added to the canvas
    And the field should display the default label "日時"
    And the field should have a date input with placeholder "日時"
    And the field should show action buttons: duplicate, drag handle, delete
    And the field should have date picker functionality
    And the save button should become enabled

  @date-field @configuration @inspector
  Scenario: FB-48 Date field inspector configuration panel
    Given I have added a date field to the canvas
    When I click on the date field to select it
    Then the inspector should show the date field configuration panel
    And I should see the following date configuration sections:
      | Section           | Field Type    | Default Value |
      | 項目タイトル      | text input    | 日時          |
      | プレースホルダー  | text input    | 日時          |
      | 項目説明文        | rich text     | empty         |
      | 回答を必須にする  | checkbox      | unchecked     |
    And I should see date format options
    And I should see date range configuration options

  @date-field @positive-input @date-picker
  Scenario Outline: FB-49 Date field supports various date formats
    Given I have added a date field to the canvas
    And I have selected the date field
    When I configure the date field for "<date_format>" format
    Then the field should accept dates in the specified format
    And the date picker should display correctly
    And the field should validate date ranges appropriately

    Examples:
      | date_format |
      | YYYY-MM-DD  |
      | MM/DD/YYYY  |
      | DD/MM/YYYY  |
      | 和暦        |

  @date-field @negative-input @date-validation
  Scenario Outline: FB-50 Date field rejects invalid dates
    Given I have added a date field to the canvas
    When I test the field with invalid date "<invalid_date>"
    Then the field should show validation error
    And the field should not accept the invalid date
    And appropriate error messaging should be displayed

    Examples:
      | invalid_date |
      | 2024-13-01   |
      | 2024-02-30   |
      | invalid-date |
      | 2024/99/99   |

  # Radio Buttons (ラジオボタン) Deep Dive Testing
  @radio-field @deep-dive @smoke @add-element
  Scenario: FB-51 Add radio button field to canvas with options
    When I drag the "ラジオボタン" element from the palette to the canvas
    Then I should see a radio field added to the canvas
    And the field should display the default label "ラジオボタン"
    And the field should have default radio options
    And the field should show action buttons: duplicate, drag handle, delete
    And the save button should become enabled

  @radio-field @configuration @inspector
  Scenario: FB-52 Radio field inspector configuration panel
    Given I have added a radio field to the canvas
    When I click on the radio field to select it
    Then the inspector should show the radio field configuration panel
    And I should see the following radio configuration sections:
      | Section           | Field Type    | Default Value |
      | 項目タイトル      | text input    | ラジオボタン  |
      | 項目説明文        | rich text     | empty         |
      | 回答を必須にする  | checkbox      | unchecked     |
    And I should see radio options management interface
    And I should be able to add, edit, and remove radio options

  @radio-field @options-management @positive-input
  Scenario: FB-53 Radio field options management
    Given I have added a radio field to the canvas
    And I have selected the radio field
    When I add a new radio option "新しい選択肢"
    Then the option should appear in the radio field
    When I edit an existing option to "編集された選択肢"
    Then the option should be updated in the radio field
    When I remove an option
    Then the option should be removed from the radio field
    And the remaining options should still function correctly

  @radio-field @validation @single-selection
  Scenario: FB-54 Radio field single selection validation
    Given I have added a radio field to the canvas with multiple options
    When I configure the field as required
    Then only one option should be selectable at a time
    And the field should validate single selection properly
    And required validation should work correctly

  # Checkboxes (チェックボックス) Deep Dive Testing
  @checkbox-field @deep-dive @smoke @add-element
  Scenario: FB-55 Add checkbox field to canvas with options
    When I drag the "チェックボックス" element from the palette to the canvas
    Then I should see a checkbox field added to the canvas
    And the field should display the default label "チェックボックス"
    And the field should have default checkbox options
    And the field should show action buttons: duplicate, drag handle, delete
    And the save button should become enabled

  @checkbox-field @configuration @inspector
  Scenario: FB-56 Checkbox field inspector configuration panel
    Given I have added a checkbox field to the canvas
    When I click on the checkbox field to select it
    Then the inspector should show the checkbox field configuration panel
    And I should see the following checkbox configuration sections:
      | Section           | Field Type      | Default Value   |
      | 項目タイトル      | text input      | チェックボックス|
      | 項目説明文        | rich text       | empty           |
      | 回答を必須にする  | checkbox        | unchecked       |
    And I should see checkbox options management interface
    And I should be able to add, edit, and remove checkbox options

  @checkbox-field @options-management @multiple-selection
  Scenario: FB-57 Checkbox field multiple selection functionality
    Given I have added a checkbox field to the canvas with multiple options
    When I configure the field with various options
    Then multiple options should be selectable simultaneously
    And the field should validate multiple selections properly
    And required validation should work for at least one selection

  @checkbox-field @validation @limits
  Scenario: FB-58 Checkbox field selection limits
    Given I have added a checkbox field to the canvas
    And I have configured multiple checkbox options
    When I set minimum and maximum selection limits
    Then the field should enforce the selection limits
    And appropriate validation messages should be displayed
    And the field should prevent invalid selections

  # Dropdown (プルダウン) Deep Dive Testing
  @dropdown-field @deep-dive @smoke @add-element
  Scenario: FB-59 Add dropdown field to canvas with options
    When I drag the "プルダウン" element from the palette to the canvas
    Then I should see a dropdown field added to the canvas
    And the field should display the default label "プルダウン"
    And the field should have a select dropdown with default options
    And the field should show action buttons: duplicate, drag handle, delete
    And the save button should become enabled

  @dropdown-field @configuration @inspector
  Scenario: FB-60 Dropdown field inspector configuration panel
    Given I have added a dropdown field to the canvas
    When I click on the dropdown field to select it
    Then the inspector should show the dropdown field configuration panel
    And I should see the following dropdown configuration sections:
      | Section           | Field Type    | Default Value |
      | 項目タイトル      | text input    | プルダウン    |
      | プレースホルダー  | text input    | 選択してください |
      | 項目説明文        | rich text     | empty         |
      | 回答を必須にする  | checkbox      | unchecked     |
    And I should see dropdown options management interface
    And I should be able to add, edit, and remove dropdown options

  @dropdown-field @options-management @selection
  Scenario: FB-61 Dropdown field options and selection functionality
    Given I have added a dropdown field to the canvas
    And I have selected the dropdown field
    When I add multiple dropdown options
    Then all options should appear in the dropdown list
    When I select an option from the dropdown
    Then the selected option should be displayed
    And only one option should be selectable at a time
    And the dropdown should close after selection

  @dropdown-field @validation @search
  Scenario: FB-62 Dropdown field with search functionality
    Given I have added a dropdown field to the canvas with many options
    When I enable search functionality for the dropdown
    Then I should be able to search and filter options
    And the search should work with both Japanese and English text
    And the filtered results should be accurate

  # File Upload (添付ファイル) Deep Dive Testing
  @file-upload-field @deep-dive @smoke @add-element
  Scenario: FB-63 Add file upload field to canvas
    When I drag the "添付ファイル" element from the palette to the canvas
    Then I should see a file upload field added to the canvas
    And the field should display the default label "添付ファイル"
    And the field should have a file upload interface
    And the field should show action buttons: duplicate, drag handle, delete
    And the save button should become enabled

  @file-upload-field @configuration @inspector
  Scenario: FB-64 File upload field inspector configuration panel
    Given I have added a file upload field to the canvas
    When I click on the file upload field to select it
    Then the inspector should show the file upload field configuration panel
    And I should see the following file upload configuration sections:
      | Section           | Field Type    | Default Value |
      | 項目タイトル      | text input    | 添付ファイル  |
      | 項目説明文        | rich text     | empty         |
      | 回答を必須にする  | checkbox      | unchecked     |
    And I should see file type restrictions configuration
    And I should see file size limit configuration
    And I should see multiple file upload options

  @file-upload-field @file-types @validation
  Scenario Outline: FB-65 File upload field type restrictions
    Given I have added a file upload field to the canvas
    And I have selected the file upload field
    When I configure allowed file types to "<file_types>"
    Then the field should only accept files of the specified types
    And appropriate validation should be applied
    And rejected files should show clear error messages

    Examples:
      | file_types                    |
      | .jpg,.png,.gif                |
      | .pdf,.doc,.docx               |
      | .txt,.csv,.xlsx               |
      | .mp4,.mov,.avi                |

  @file-upload-field @file-size @validation
  Scenario Outline: FB-66 File upload field size restrictions
    Given I have added a file upload field to the canvas
    And I have selected the file upload field
    When I configure maximum file size to "<max_size>"
    Then the field should enforce the size limit
    And files exceeding the limit should be rejected
    And appropriate error messages should be displayed

    Examples:
      | max_size |
      | 1MB      |
      | 5MB      |
      | 10MB     |
      | 50MB     |

  @file-upload-field @multiple-files @functionality
  Scenario: FB-67 File upload field multiple file support
    Given I have added a file upload field to the canvas
    And I have selected the file upload field
    When I enable multiple file upload
    Then the field should accept multiple files
    And each file should be validated individually
    And I should be able to remove individual files
    And the total file count should be managed properly

  @file-upload-field @drag-drop @functionality
  Scenario: FB-68 File upload field drag and drop functionality
    Given I have added a file upload field to the canvas
    When I test the drag and drop functionality
    Then files should be uploadable via drag and drop
    And the drag and drop area should provide visual feedback
    And dropped files should be validated according to configured rules
    And the upload progress should be displayed appropriately

  @file-upload-field @error-handling @edge-cases
  Scenario: FB-69 File upload field error handling
    Given I have added a file upload field to the canvas
    When I test various error scenarios
    Then network errors should be handled gracefully
    And file corruption should be detected
    And upload failures should provide clear feedback
    And users should be able to retry failed uploads
    And the field should maintain stability during errors

  # Name Field (氏名) Deep Dive Testing
  @name-field @deep-dive @smoke @add-element
  Scenario: FB-70 Add name field to canvas with validation
    When I drag the "氏名" element from the palette to the canvas
    Then I should see a name field added to the canvas
    And the field should display the default label "氏名"
    And the field should have name input components
    And the field should show action buttons: duplicate, drag handle, delete
    And the save button should become enabled

  @name-field @configuration @inspector
  Scenario: FB-71 Name field inspector configuration panel
    Given I have added a name field to the canvas
    When I click on the name field to select it
    Then the inspector should show the name field configuration panel
    And I should see the following name configuration sections:
      | Section           | Field Type    | Default Value |
      | 項目タイトル      | text input    | 氏名          |
      | 項目説明文        | rich text     | empty         |
      | 回答を必須にする  | checkbox      | unchecked     |
    And I should see name field specific options
    And the field should support Japanese name formatting

  @name-field @validation @japanese-names
  Scenario Outline: FB-72 Name field supports various name formats
    Given I have added a name field to the canvas
    And I have selected the name field
    When I configure the name field for "<name_format>" format
    Then the field should accept names in the specified format
    And the field should validate name components appropriately
    And appropriate formatting should be applied

    Examples:
      | name_format |
      | 姓名分離    |
      | フルネーム  |
      | ふりがな付き|

  # Address Field (住所) Deep Dive Testing
  @address-field @deep-dive @smoke @add-element
  Scenario: FB-73 Add address field to canvas with components
    When I drag the "住所" element from the palette to the canvas
    Then I should see an address field added to the canvas
    And the field should display the default label "住所"
    And the field should have address input components
    And the field should show action buttons: duplicate, drag handle, delete
    And the save button should become enabled

  @address-field @configuration @inspector
  Scenario: FB-74 Address field inspector configuration panel
    Given I have added an address field to the canvas
    When I click on the address field to select it
    Then the inspector should show the address field configuration panel
    And I should see the following address configuration sections:
      | Section           | Field Type    | Default Value |
      | 項目タイトル      | text input    | 住所          |
      | 項目説明文        | rich text     | empty         |
      | 回答を必須にする  | checkbox      | unchecked     |
    And I should see address field specific options
    And the field should support Japanese address formatting

  @address-field @validation @japanese-address
  Scenario Outline: FB-75 Address field supports Japanese address formats
    Given I have added an address field to the canvas
    And I have selected the address field
    When I configure the address field for "<address_format>" format
    Then the field should accept addresses in the specified format
    And the field should validate address components appropriately
    And postal code integration should work correctly

    Examples:
      | address_format |
      | 都道府県市区町村|
      | 郵便番号連携    |
      | 番地建物名分離  |

  # Birth Date Field (生年月日) Deep Dive Testing
  @birth-date-field @deep-dive @smoke @add-element
  Scenario: FB-76 Add birth date field to canvas with validation
    When I drag the "生年月日" element from the palette to the canvas
    Then I should see a birth date field added to the canvas
    And the field should display the default label "生年月日"
    And the field should have birth date input components
    And the field should show action buttons: duplicate, drag handle, delete
    And the save button should become enabled

  @birth-date-field @configuration @inspector
  Scenario: FB-77 Birth date field inspector configuration panel
    Given I have added a birth date field to the canvas
    When I click on the birth date field to select it
    Then the inspector should show the birth date field configuration panel
    And I should see the following birth date configuration sections:
      | Section           | Field Type    | Default Value |
      | 項目タイトル      | text input    | 生年月日      |
      | 項目説明文        | rich text     | empty         |
      | 回答を必須にする  | checkbox      | unchecked     |
    And I should see birth date field specific options
    And the field should support age calculation

  @birth-date-field @validation @age-validation
  Scenario Outline: FB-78 Birth date field validates age ranges
    Given I have added a birth date field to the canvas
    And I have selected the birth date field
    When I configure the birth date field with "<age_constraint>"
    Then the field should enforce the age constraint
    And appropriate validation messages should be displayed
    And the field should handle edge cases correctly

    Examples:
      | age_constraint |
      | 最小年齢18歳   |
      | 最大年齢100歳  |
      | 成人のみ       |
      | 未成年のみ     |

  # Preview Functionality Deep Dive Testing
  @preview @single-element @smoke @desktop-view
  Scenario Outline: FB-79 Preview single element in desktop view
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "<element_name>" element from the palette to the canvas
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    And I should see the desktop view selected by default
    And I should see 1 form element in the preview
    And the "<element_name>" element should be displayed correctly in desktop view
    And the element should match the configuration from the editor

    Examples:
      | element_name |
      | テキスト     |
      | 段落テキスト |
      | メールアドレス |
      | 電話番号     |
      | 日時         |
      | 氏名         |
      | 住所         |
      | 生年月日     |
      | ラジオボタン |
      | チェックボックス |
      | プルダウン   |
      | 添付ファイル |

  @preview @single-element @tablet-view
  Scenario Outline: FB-80 Preview single element in tablet view
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "<element_name>" element from the palette to the canvas
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    When I click the tablet view button
    Then I should see the tablet view selected
    And I should see 1 form element in the preview
    And the "<element_name>" element should be displayed correctly in tablet view
    And the element should be responsive for tablet screen size

    Examples:
      | element_name |
      | テキスト     |
      | メールアドレス |
      | ラジオボタン |
      | プルダウン   |

  @preview @single-element @mobile-view
  Scenario Outline: FB-81 Preview single element in mobile view
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "<element_name>" element from the palette to the canvas
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    When I click the mobile view button
    Then I should see the mobile view selected
    And I should see 1 form element in the preview
    And the "<element_name>" element should be displayed correctly in mobile view
    And the element should be responsive for mobile screen size

    Examples:
      | element_name |
      | テキスト     |
      | メールアドレス |
      | ラジオボタン |
      | プルダウン   |

  @preview @multiple-elements @desktop-view
  Scenario: FB-82 Preview multiple elements in desktop view
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "テキスト" element from the palette to the canvas
    And I drag the "メールアドレス" element from the palette to the canvas
    And I drag the "ラジオボタン" element from the palette to the canvas
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    And I should see the desktop view selected by default
    And I should see 3 form elements in the preview
    And all elements should be displayed correctly in desktop view
    And the elements should maintain their order from the editor

  @preview @multiple-elements @tablet-view
  Scenario: FB-83 Preview multiple elements in tablet view
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "テキスト" element from the palette to the canvas
    And I drag the "メールアドレス" element from the palette to the canvas
    And I drag the "ラジオボタン" element from the palette to the canvas
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    When I click the tablet view button
    Then I should see the tablet view selected
    And I should see 3 form elements in the preview
    And all elements should be displayed correctly in tablet view
    And the elements should be responsive for tablet screen size

  @preview @multiple-elements @mobile-view
  Scenario: FB-84 Preview multiple elements in mobile view
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "テキスト" element from the palette to the canvas
    And I drag the "メールアドレス" element from the palette to the canvas
    And I drag the "ラジオボタン" element from the palette to the canvas
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    When I click the mobile view button
    Then I should see the mobile view selected
    And I should see 3 form elements in the preview
    And all elements should be displayed correctly in mobile view
    And the elements should be responsive for mobile screen size

  @preview @device-switching @comprehensive
  Scenario: FB-85 Preview with device view switching
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "テキスト" element from the palette to the canvas
    And I drag the "メールアドレス" element from the palette to the canvas
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    And I should see the desktop view selected by default
    When I click the tablet view button
    Then I should see the tablet view selected
    And the form should adapt to tablet layout
    When I click the mobile view button
    Then I should see the mobile view selected
    And the form should adapt to mobile layout
    When I click the desktop view button
    Then I should see the desktop view selected
    And the form should adapt back to desktop layout

  @preview @form-functionality @interaction
  Scenario: FB-86 Preview form functionality and interaction
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "テキスト" element from the palette to the canvas
    And I drag the "ラジオボタン" element from the palette to the canvas
    And I drag the "チェックボックス" element from the palette to the canvas
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    And I should be able to interact with the text field
    And I should be able to select radio button options
    And I should be able to check/uncheck checkbox options
    And all form elements should be functional in preview mode

  @preview @configuration-reflection @validation
  Scenario: FB-87 Preview reflects editor configuration changes
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "テキスト" element from the palette to the canvas
    And I select the text field
    And I change the field label to "カスタムテキスト"
    And I change the field placeholder to "ここに入力してください"
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    And the text field should display the label "カスタムテキスト"
    And the text field should have placeholder "ここに入力してください"
    And the preview should match the editor configuration exactly

  @preview @navigation @back-to-editor
  Scenario: FB-88 Preview navigation and return to editor
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "テキスト" element from the palette to the canvas
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    And I should see navigation options to return to editor
    When I navigate back to the editor
    Then I should be back on the form builder editor page
    And the form should still contain the text element
    And all editor functionality should be available

  @preview @responsive-design @layout-validation
  Scenario: FB-89 Preview responsive design validation
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "住所" element from the palette to the canvas
    And I drag the "氏名" element from the palette to the canvas
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    When I test all device views
    Then the address field should display all components correctly in each view
    And the name field should display all components correctly in each view
    And the layout should be optimized for each device type
    And no elements should be cut off or overlapping

  # Enhanced Single Element Preview Tests - Comprehensive Device View Testing
  @preview @single-element @enhanced @comprehensive
  Scenario Outline: FB-99 Enhanced single element preview across all device views
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "<element_name>" element from the palette to the canvas
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    And I should see the desktop view selected by default
    And I should see 1 form element in the preview
    # Test Desktop View
    And the "<element_name>" element should be displayed correctly in desktop view
    And the element should match the configuration from the editor
    # Test Tablet View
    When I click the tablet view button
    Then I should see the tablet view selected
    And I should see 1 form element in the preview
    And the "<element_name>" element should be displayed correctly in tablet view
    And the element should be responsive for tablet screen size
    # Test Mobile View
    When I click the mobile view button
    Then I should see the mobile view selected
    And I should see 1 form element in the preview
    And the "<element_name>" element should be displayed correctly in mobile view
    And the element should be responsive for mobile screen size
    # Return to Desktop to verify consistency
    When I click the desktop view button
    Then I should see the desktop view selected
    And the "<element_name>" element should be displayed correctly in desktop view

    Examples:
      | element_name     |
      | テキスト         |
      | 段落テキスト     |
      | メールアドレス   |
      | 電話番号         |
      | 日時             |
      | 氏名             |
      | 住所             |
      | 生年月日         |
      | ラジオボタン     |
      | チェックボックス |
      | プルダウン       |
      | 添付ファイル     |

  # Enhanced Multiple Element Preview Tests - Comprehensive Device View Testing
  @preview @multiple-elements @enhanced @comprehensive
  Scenario: FB-100 Enhanced multiple elements preview with comprehensive device testing
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "テキスト" element from the palette to the canvas
    And I drag the "メールアドレス" element from the palette to the canvas
    And I drag the "ラジオボタン" element from the palette to the canvas
    And I drag the "チェックボックス" element from the palette to the canvas
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    And I should see 4 form elements in the preview
    # Desktop View Testing
    And I should see the desktop view selected by default
    And all elements should be displayed correctly in desktop view
    And the elements should maintain their order from the editor
    And no elements should be cut off or overlapping in desktop view
    # Tablet View Testing
    When I click the tablet view button
    Then I should see the tablet view selected
    And I should see 4 form elements in the preview
    And all elements should be displayed correctly in tablet view
    And the elements should be responsive for tablet screen size
    And the elements should maintain their order from the editor
    And no elements should be cut off or overlapping in tablet view
    # Mobile View Testing
    When I click the mobile view button
    Then I should see the mobile view selected
    And I should see 4 form elements in the preview
    And all elements should be displayed correctly in mobile view
    And the elements should be responsive for mobile screen size
    And the elements should maintain their order from the editor
    And no elements should be cut off or overlapping in mobile view
    # Return to Desktop to verify consistency
    When I click the desktop view button
    Then I should see the desktop view selected
    And I should see 4 form elements in the preview
    And all elements should be displayed correctly in desktop view

  # Enhanced Device View Switching Validation
  @preview @device-switching @enhanced @comprehensive
  Scenario: FB-101 Comprehensive device view switching with element validation
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "テキスト" element from the palette to the canvas
    And I drag the "ラジオボタン" element from the palette to the canvas
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    # Validate Device View Controls
    And I should see the device view control buttons
    And the desktop view button should have ComputerIcon
    And the tablet view button should have TabletMacIcon
    And the mobile view button should have SmartphoneIcon
    And I should see the desktop view selected by default
    # Test Multiple View Switching Cycles
    When I switch between all device views multiple times
    Then each view should display elements appropriately
    And no elements should be cut off or overlapping
    And the form should adapt layout for each device type
    And I should see 2 form elements in the preview in all views
    # Validate Specific View Behaviors
    When I click the tablet view button
    Then the form layout should be optimized for tablet interaction
    And touch targets should be appropriately sized
    When I click the mobile view button
    Then the form layout should be optimized for mobile interaction
    And elements should stack vertically for mobile
    When I click the desktop view button
    Then the form layout should be optimized for desktop interaction
    And elements should use available horizontal space efficiently

  # Enhanced Element Configuration Preservation Tests
  @preview @configuration-preservation @enhanced @comprehensive
  Scenario: FB-102 Element configurations preserved across all device views
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "テキスト" element from the palette to the canvas
    And I select the text field
    And I change the field label to "カスタムテキスト"
    And I change the field placeholder to "ここに入力してください"
    And I check the "回答を必須にする" checkbox
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    # Desktop View Configuration Validation
    And I should see the desktop view selected by default
    And the text field should display the label "カスタムテキスト"
    And the text field should have placeholder "ここに入力してください"
    And the text field should show required indicator
    # Tablet View Configuration Validation
    When I click the tablet view button
    Then I should see the tablet view selected
    And the text field should display the label "カスタムテキスト"
    And the text field should have placeholder "ここに入力してください"
    And the text field should show required indicator
    # Mobile View Configuration Validation
    When I click the mobile view button
    Then I should see the mobile view selected
    And the text field should display the label "カスタムテキスト"
    And the text field should have placeholder "ここに入力してください"
    And the text field should show required indicator
    # Return to Desktop and verify persistence
    When I click the desktop view button
    Then I should see the desktop view selected
    And the text field should display the label "カスタムテキスト"
    And the text field should have placeholder "ここに入力してください"
    And the text field should show required indicator

  @preview @error-handling @edge-cases
  Scenario: FB-90 Preview error handling and edge cases
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I try to preview an empty form
    Then I should see appropriate messaging for empty form
    When I drag the "テキスト" element from the palette to the canvas
    And I click the preview button without saving
    Then I should see a save prompt or the form should auto-save
    And the preview should display the current form state

  # === ENHANCED COMPREHENSIVE PREVIEW TESTING ===

  @preview @single-element @comprehensive @all-views
  Scenario Outline: FB-91 Comprehensive single element preview across all views
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "<element_name>" element from the palette to the canvas
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    And I should see the desktop view selected by default
    And I should see 1 form element in the preview
    And the "<element_name>" element should be displayed correctly in desktop view
    When I click the tablet view button
    Then I should see the tablet view selected
    And I should see 1 form element in the preview
    And the "<element_name>" element should be displayed correctly in tablet view
    And the element should be responsive for tablet screen size
    When I click the mobile view button
    Then I should see the mobile view selected
    And I should see 1 form element in the preview
    And the "<element_name>" element should be displayed correctly in mobile view
    And the element should be responsive for mobile screen size
    When I click the desktop view button
    Then I should see the desktop view selected
    And the element should return to desktop layout

    Examples:
      | element_name |
      | テキスト     |
      | 段落テキスト |
      | メールアドレス |
      | 電話番号     |
      | 日時         |
      | 氏名         |
      | 住所         |
      | 生年月日     |
      | ラジオボタン |
      | チェックボックス |
      | プルダウン   |
      | 添付ファイル |

  @preview @multiple-elements @comprehensive @all-views
  Scenario: FB-92 Multiple elements preview with comprehensive view testing
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "テキスト" element from the palette to the canvas
    And I drag the "メールアドレス" element from the palette to the canvas
    And I drag the "ラジオボタン" element from the palette to the canvas
    And I drag the "チェックボックス" element from the palette to the canvas
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    And I should see the desktop view selected by default
    And I should see 4 form elements in the preview
    And all elements should be displayed correctly in desktop view
    And the elements should maintain their order from the editor
    When I click the tablet view button
    Then I should see the tablet view selected
    And I should see 4 form elements in the preview
    And all elements should be displayed correctly in tablet view
    And all elements should be responsive for tablet screen size
    When I click the mobile view button
    Then I should see the mobile view selected
    And I should see 4 form elements in the preview
    And all elements should be displayed correctly in mobile view
    And all elements should be responsive for mobile screen size
    And the layout should stack elements appropriately for mobile
    When I click the desktop view button
    Then I should see the desktop view selected
    And all elements should return to desktop layout

  @preview @complex-elements @configuration-validation
  Scenario: FB-93 Preview complex elements with custom configurations
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "ラジオボタン" element from the palette to the canvas
    And I select the radio button field
    And I configure the radio button with options "選択肢1,選択肢2,選択肢3"
    And I set the field label to "カスタムラジオボタン"
    And I set the field as required
    And I drag the "チェックボックス" element from the palette to the canvas
    And I select the checkbox field
    And I configure the checkbox with options "オプション1,オプション2,オプション3"
    And I set the field label to "カスタムチェックボックス"
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    And the radio button should display label "カスタムラジオボタン"
    And the radio button should show 3 options
    And the radio button should display as required
    And the checkbox should display label "カスタムチェックボックス"
    And the checkbox should show 3 options
    And I should be able to interact with all radio options
    And I should be able to interact with all checkbox options
    When I test all device views
    Then all configurations should be preserved across views
    And all interactive elements should remain functional

  @preview @edge-cases @empty-required-fields
  Scenario: FB-94 Preview with required fields and validation
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "テキスト" element from the palette to the canvas
    And I select the text field
    And I set the field as required
    And I set the field label to "必須テキスト"
    And I drag the "メールアドレス" element from the palette to the canvas
    And I select the email field
    And I set the field as required
    And I set the field label to "必須メールアドレス"
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    And the required text field should display required indicator
    And the required email field should display required indicator
    When I test form submission without filling required fields
    Then I should see validation errors for required fields
    When I fill in the required fields with valid data
    And I submit the form
    Then the form should submit successfully
    When I test all device views
    Then validation behavior should be consistent across views

  @preview @layout-stress-test @many-elements
  Scenario: FB-95 Preview with many elements layout stress test
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I add multiple elements to create a complex form:
      | element_type | position |
      | テキスト     | 1        |
      | 段落テキスト | 2        |
      | メールアドレス | 3        |
      | 電話番号     | 4        |
      | 日時         | 5        |
      | 氏名         | 6        |
      | 住所         | 7        |
      | ラジオボタン | 8        |
      | チェックボックス | 9        |
      | プルダウン   | 10       |
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    And I should see 10 form elements in the preview
    And all elements should be displayed in the correct order
    When I test all device views
    Then all elements should be visible and accessible in each view
    And no elements should be cut off or overlapping
    And scrolling should work properly if needed
    And the layout should be optimized for each device type

  @preview @navigation @editor-flow @comprehensive
  Scenario: FB-96 Complete preview flow with editor navigation
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "テキスト" element from the palette to the canvas
    And I select the text field
    And I set the field label to "サンプルテキスト"
    And I set the field as required
    And I drag the "ラジオボタン" element from the palette to the canvas
    And I select the radio button field
    And I configure the radio button with options "選択1,選択2,選択3"
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    And I should see the desktop view selected by default
    And I should see 2 form elements in the preview
    And the text field should display label "サンプルテキスト"
    And the text field should display as required
    And the radio button should show 3 options
    When I test all device views
    Then all configurations should be preserved across views
    And all interactive elements should remain functional
    When I navigate back to the editor
    Then I should be back on the form builder editor page
    And the form should still contain both elements
    And all element configurations should be preserved in editor
    And all editor functionality should be available

  @preview @back-navigation @state-persistence
  Scenario: FB-97 Preview back navigation preserves editor state
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "メールアドレス" element from the palette to the canvas
    And I drag the "チェックボックス" element from the palette to the canvas
    And I select the checkbox field
    And I configure the checkbox with options "オプションA,オプションB"
    And I save the form
    And I click the preview button
    Then I should see the preview page loaded
    When I switch between all device views multiple times
    And I navigate back to the editor
    Then I should be back on the form builder editor page
    And the form should contain exactly 2 elements
    And the checkbox configuration should be preserved
    And I should be able to continue editing without issues
    When I click the preview button again
    Then the preview should show the same form as before
    And all previous configurations should still be intact

  @preview @unsaved-changes @auto-save
  Scenario: FB-98 Preview with unsaved changes handling
    Given I am an authenticated user
    And I navigate to the Form Builder "Editor" page for an existing form
    And I wait for the app header and left palette to be visible
    When I drag the "段落テキスト" element from the palette to the canvas
    And I select the textarea field
    And I set the field label to "コメント"
    And I do not save the form
    And I click the preview button
    Then I should see a save prompt or auto-save indicator
    And the preview should load with current form state
    And the textarea should display label "コメント"
    When I navigate back to the editor
    Then the form should retain all unsaved changes
    And the textarea should still have the label "コメント"
