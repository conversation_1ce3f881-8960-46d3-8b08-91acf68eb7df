const common = {
  requireModule: ['ts-node/register'],
  require: [
    'tests/step-definitions/*.steps.ts',
    'tests/step-definitions/common-steps.ts',
    'tests/step-definitions/hooks.ts'
  ],
  format: [
    'progress-bar',
    'json:reports/output/cucumber-report.json',
    'html:reports/output/cucumber-report.html',
    '@cucumber/pretty-formatter',
  ],
  formatOptions: {
    snippetInterface: 'async-await',
  },
};

module.exports = {
  default: {
    ...common,
    paths: ['tests/features/**/*.feature'],
    parallel: 1,
  },
  parallel: {
    ...common,
    paths: ['tests/features/**/*.feature'],
    parallel: 3,
  },
  dev: {
    ...common,
    paths: ['tests/features/**/*.feature'],
    tags: 'not @skip and not @prod-only',
    parallel: 1,
  },
  staging: {
    ...common,
    paths: ['tests/features/**/*.feature'],
    tags: 'not @skip and not @dev-only',
    parallel: 2,
  },
  prod: {
    ...common,
    paths: ['tests/features/**/*.feature'],
    tags: 'not @skip and not @dev-only and not @staging-only',
    parallel: 1,
  },
  smoke: {
    ...common,
    paths: ['tests/features/**/*.feature'],
    tags: '@smoke',
    parallel: 1,
  },
  login: {
    ...common,
    paths: ['tests/features/login/*.feature'],
    tags: '@login',
    parallel: 1,
  },
  regression: {
    ...common,
    paths: ['tests/features/**/*.feature'],
    tags: '@regression',
    parallel: 3,
  },
  forms: {
    requireModule: ['ts-node/register'],
    require: [
      'tests/step-definitions/*.steps.ts',
      'tests/step-definitions/common-steps.ts',
      'tests/step-definitions/hooks.ts'
    ],
    paths: ['tests/features/forms/*.feature'],
    tags: process.env.TAGS || '@forms',
    parallel: 1,
    format: ['progress', 'json:reports/output/cucumber-forms.json'],
    failFast: false,
  },
  'forms-quick': {
    requireModule: ['ts-node/register'],
    require: [
      'tests/step-definitions/*.steps.ts',
      'tests/step-definitions/common-steps.ts',
      'tests/step-definitions/hooks.ts'
    ],
    paths: ['tests/features/forms/*.feature'],
    tags: '@forms and not @perf',
    parallel: 1,
    format: ['progress', 'json:reports/output/cucumber-forms-quick.json'],
    failFast: false,
  },
};


